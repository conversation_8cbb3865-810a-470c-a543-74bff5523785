pipeline_type: standard  # 使用standard而不是multi-scale以节省内存
checkpoint_path: "LTXV_models/LTX-Video/ltxv-13b-0.9.7-dev.safetensors"
stg_mode: "attention_values"
decode_timestep: 0.05
decode_noise_scale: 0.025
text_encoder_model_name_or_path: "LTXV_models/LTX-Video"
precision: "bfloat16"
sampler: "from_checkpoint"
prompt_enhancement_words_threshold: 0  # 禁用提示词增强以节省内存
prompt_enhancer_image_caption_model_name_or_path: "MiaoshouAI/Florence-2-large-PromptGen-v2.0"
prompt_enhancer_llm_model_name_or_path: "unsloth/Llama-3.2-3B-Instruct"
stochastic_sampling: false

# 使用13B模型的第一阶段参数（简化版）
guidance_scale: [1, 1, 4, 5, 4, 1, 1]  # 降低guidance scale以节省内存
stg_scale: [0, 0, 2, 3, 2, 1, 0]       # 降低STG scale
rescaling_scale: [1, 1, 0.7, 0.7, 1, 1, 1]
guidance_timesteps: [1.0, 0.996, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180]
skip_block_list: [[], [11, 25, 35, 39], [22, 35, 39], [28], [28], [28], [28]]
num_inference_steps: 25  # 减少推理步数
skip_final_inference_steps: 2
cfg_star_rescale: true

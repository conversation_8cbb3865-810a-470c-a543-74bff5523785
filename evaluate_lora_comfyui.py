#!/usr/bin/env python3
"""
LoRA训练效果评估脚本 (ComfyUI版本)

该脚本专门为ComfyUI工作流设计，支持：
1. 自动转换LoRA格式（Diffusers -> ComfyUI）
2. 生成ComfyUI工作流JSON文件
3. 通过ComfyUI API或命令行执行推理
4. 批量处理多个checkpoint和测试视频

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import cv2
import json
import argparse
import subprocess
from pathlib import Path
from typing import List, Dict, Optional
import logging
from tqdm import tqdm
import re
from datetime import datetime
import requests
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lora_evaluation_comfyui.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComfyUILoRAEvaluator:
    """ComfyUI版LoRA评估器"""
    
    def __init__(self, 
                 checkpoints_dir: str,
                 base_model_path: str,
                 test_videos_dir: str,
                 prompt_file: str,
                 output_dir: str,
                 comfyui_path: str,
                 comfyui_api_url: str = "http://127.0.0.1:8188"):
        
        self.checkpoints_dir = Path(checkpoints_dir)
        self.base_model_path = Path(base_model_path)
        self.test_videos_dir = Path(test_videos_dir)
        self.prompt_file = Path(prompt_file)
        self.output_dir = Path(output_dir)
        self.comfyui_path = Path(comfyui_path)
        self.comfyui_api_url = comfyui_api_url
        
        self.checkpoints = []
        self.test_videos = []
        self.prompts = {}
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # ComfyUI相关路径
        self.comfyui_models_loras = self.comfyui_path / "models" / "loras"
        self.comfyui_models_checkpoints = self.comfyui_path / "models" / "checkpoints"
        self.comfyui_input = self.comfyui_path / "input"
        self.comfyui_output = self.comfyui_path / "output"
        
    def setup(self) -> bool:
        """初始化设置"""
        logger.info("开始初始化ComfyUI评估环境...")
        
        # 检查必要路径
        if not self._check_paths():
            return False
            
        # 扫描checkpoints
        if not self._scan_checkpoints():
            return False
            
        # 扫描测试视频
        if not self._scan_test_videos():
            return False
            
        # 加载提示词
        if not self._load_prompts():
            return False
            
        logger.info(f"找到 {len(self.checkpoints)} 个checkpoints")
        logger.info(f"找到 {len(self.test_videos)} 个测试视频")
        logger.info(f"加载了 {len(self.prompts)} 个提示词")
        
        return True
    
    def _check_paths(self) -> bool:
        """检查必要路径是否存在"""
        paths_to_check = [
            (self.checkpoints_dir, "Checkpoints目录"),
            (self.base_model_path, "基础模型路径"),
            (self.test_videos_dir, "测试视频目录"),
            (self.prompt_file, "提示词文件"),
            (self.comfyui_path, "ComfyUI路径")
        ]
        
        for path, name in paths_to_check:
            if not path.exists():
                logger.error(f"{name} 不存在: {path}")
                return False
        
        # 检查ComfyUI关键目录
        comfyui_dirs = [
            self.comfyui_models_loras,
            self.comfyui_models_checkpoints,
            self.comfyui_input,
            self.comfyui_output
        ]
        
        for dir_path in comfyui_dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
                
        return True
    
    def _scan_checkpoints(self) -> bool:
        """扫描checkpoint文件"""
        # 查找所有.safetensors文件
        checkpoint_files = list(self.checkpoints_dir.glob("*.safetensors"))
        
        if not checkpoint_files:
            logger.error(f"在 {self.checkpoints_dir} 中未找到任何.safetensors文件")
            return False
        
        # 按步数排序
        def extract_step_number(filename: str) -> int:
            match = re.search(r'step_(\d+)', filename)
            return int(match.group(1)) if match else 0
        
        checkpoint_files.sort(key=lambda x: extract_step_number(x.name))
        
        self.checkpoints = [
            {
                'path': checkpoint_file,
                'name': checkpoint_file.stem,
                'step': extract_step_number(checkpoint_file.name)
            }
            for checkpoint_file in checkpoint_files
        ]
        
        return True
    
    def _scan_test_videos(self) -> bool:
        """扫描测试视频文件"""
        # 查找所有.mp4文件
        video_files = list(self.test_videos_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error(f"在 {self.test_videos_dir} 中未找到任何.mp4文件")
            return False
        
        self.test_videos = [
            {
                'path': video_file,
                'name': video_file.stem,
                'filename': video_file.name
            }
            for video_file in video_files
        ]
        
        return True
    
    def _load_prompts(self) -> bool:
        """加载提示词文件"""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析表格格式的提示词
            lines = content.strip().split('\n')
            for line in lines[2:]:  # 跳过表头
                if '|' in line:
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 4:
                        example_num = parts[1]
                        motion_description = parts[3]
                        
                        # 提取视频文件名模式
                        if 'urbanvideo_test' in motion_description or '00819' in example_num:
                            self.prompts['00819_urbanvideo_test'] = motion_description
                        elif '00840' in example_num:
                            self.prompts['00840_urbanvideo_test'] = motion_description
                        elif '00846' in example_num:
                            self.prompts['00846_urbanvideo_test'] = motion_description
                        elif '01035' in example_num:
                            self.prompts['01035_NAT2021_test_N02029_4'] = motion_description
                        elif '01374' in example_num:
                            self.prompts['01374_NAT2021_test_N08024_3'] = motion_description
            
            return len(self.prompts) > 0
            
        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
            return False
    
    def extract_first_frame(self, video_path: Path, output_path: Path) -> bool:
        """从视频中提取首帧"""
        try:
            cap = cv2.VideoCapture(str(video_path))
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                cv2.imwrite(str(output_path), frame)
                return True
            else:
                logger.error(f"无法从视频中读取帧: {video_path}")
                return False
                
        except Exception as e:
            logger.error(f"提取首帧失败: {e}")
            return False
    
    def convert_lora_to_comfyui(self, lora_path: Path) -> Optional[Path]:
        """将LoRA转换为ComfyUI格式"""
        comfyui_lora_name = f"{lora_path.stem}_comfy.safetensors"
        comfyui_lora_path = self.comfyui_models_loras / comfyui_lora_name
        
        # 如果已经存在，直接返回
        if comfyui_lora_path.exists():
            logger.info(f"ComfyUI LoRA已存在: {comfyui_lora_name}")
            return comfyui_lora_path
        
        # 使用转换脚本
        trainer_path = self.base_model_path.parent / "LTX-Video-Trainer"
        convert_script = trainer_path / "scripts" / "convert_checkpoint.py"
        
        if not convert_script.exists():
            logger.error(f"转换脚本不存在: {convert_script}")
            return None
        
        try:
            cmd = [
                sys.executable, str(convert_script),
                str(lora_path),
                "--to-comfy",
                "--output-path", str(comfyui_lora_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"成功转换LoRA: {lora_path.name} -> {comfyui_lora_name}")
            return comfyui_lora_path
            
        except subprocess.CalledProcessError as e:
            logger.error(f"转换LoRA失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            return None

    def create_comfyui_workflow(self, lora_name: str, image_name: str, prompt: str) -> Dict:
        """创建ComfyUI工作流JSON"""

        workflow = {
            "1": {
                "inputs": {
                    "ckpt_name": "ltxv-2b-0.9.6-dev-04-25.safetensors"
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load Checkpoint"}
            },
            "2": {
                "inputs": {
                    "text": prompt,
                    "clip": ["5", 1]  # 使用LoRA加载后的CLIP
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Prompt)"}
            },
            "3": {
                "inputs": {
                    "text": "",
                    "clip": ["5", 1]  # 使用LoRA加载后的CLIP
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Negative)"}
            },
            "4": {
                "inputs": {
                    "image": image_name,
                    "upload": "image"
                },
                "class_type": "LoadImage",
                "_meta": {"title": "Load Image"}
            },
            "5": {
                "inputs": {
                    "lora_name": lora_name,
                    "strength_model": 1.0,
                    "strength_clip": 1.0,
                    "model": ["1", 0],
                    "clip": ["1", 1]
                },
                "class_type": "LoraLoader",
                "_meta": {"title": "Load LoRA"}
            },
            "6": {
                "inputs": {
                    "width": 1216,
                    "height": 704,
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Empty Latent Image"}
            },
            "7": {
                "inputs": {
                    "seed": 42,
                    "steps": 40,
                    "cfg": 3.0,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 1.0,
                    "model": ["5", 0],  # 使用LoRA加载后的模型
                    "positive": ["2", 0],
                    "negative": ["3", 0],
                    "latent_image": ["6", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "KSampler"}
            },
            "8": {
                "inputs": {
                    "samples": ["7", 0],
                    "vae": ["1", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE Decode"}
            },
            "9": {
                "inputs": {
                    "filename_prefix": "ltxv_lora_output",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage",
                "_meta": {"title": "Save Image"}
            }
        }

        return workflow

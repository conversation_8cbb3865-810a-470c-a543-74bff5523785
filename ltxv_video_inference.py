#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LTX-Video 视频处理推理代码
支持从视频文件中提取首帧，并使用prompt.txt中的描述进行推理

使用方法:
python ltxv_video_inference.py --video_dir demo_video/airscape --prompt_file demo_video/prompt.txt --single_video 00819_urbanvideo_test.mp4
"""

import os
import gc
import torch
import argparse
import logging
import sys
import random
import numpy as np
import cv2
import re
from pathlib import Path
from datetime import datetime

# 添加LTX-Video路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'LTX-Video'))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('ltxv_video_inference.log')
    ]
)
logger = logging.getLogger('LTXV-Video')

# 导入LTX-Video相关模块
from ltx_video.inference import infer, InferenceConfig


def extract_first_frame(video_path, output_path=None):
    """从视频文件中提取第一帧"""
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        ret, frame = cap.read()
        if not ret:
            raise ValueError(f"无法读取视频帧: {video_path}")
        
        if output_path:
            cv2.imwrite(output_path, frame)
            logger.info(f"第一帧已保存到: {output_path}")
        
        cap.release()
        return frame
    except Exception as e:
        logger.error(f"提取视频帧时出错: {e}")
        raise


def parse_prompt_txt(prompt_file_path):
    """解析prompt.txt文件，提取视频ID和对应的motion intention"""
    if not os.path.exists(prompt_file_path):
        raise FileNotFoundError(f"Prompt文件不存在: {prompt_file_path}")
    
    prompts_dict = {}
    
    try:
        with open(prompt_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式匹配表格行
        # 匹配格式: | **数字** | <img src="...filename.gif"... | motion intention |
        pattern = r'\|\s*\*\*(\d+)\*\*\s*\|.*?([^/]+?)\.gif.*?\|\s*([^|]+?)\s*\|'
        matches = re.findall(pattern, content, re.DOTALL)
        
        for match in matches:
            example_num, filename, motion_intention = match
            # 清理motion intention文本
            motion_intention = motion_intention.strip().replace('\n', ' ')
            motion_intention = re.sub(r'\s+', ' ', motion_intention)  # 合并多个空格
            
            # 将filename转换为对应的视频文件名
            video_filename = filename + '.mp4'
            prompts_dict[video_filename] = motion_intention
            
            logger.info(f"解析到prompt - {video_filename}: {motion_intention[:100]}...")
        
        logger.info(f"从 {prompt_file_path} 解析到 {len(prompts_dict)} 个prompt")
        return prompts_dict
        
    except Exception as e:
        logger.error(f"解析prompt文件时出错: {e}")
        raise


def get_gpu_memory_info():
    """获取GPU内存使用信息"""
    if not torch.cuda.is_available():
        return {"cuda": "不可用"}
    
    memory_info = {
        "allocated": torch.cuda.memory_allocated() / 1024**3,  # GB
        "reserved": torch.cuda.memory_reserved() / 1024**3,    # GB
        "max_allocated": torch.cuda.max_memory_allocated() / 1024**3,  # GB
        "total": torch.cuda.get_device_properties(0).total_memory / 1024**3,  # GB
        "free": (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated()) / 1024**3
    }
    return memory_info


def setup_environment():
    """设置基本环境"""
    # 基本的CUDA设置
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.backends.cudnn.benchmark = True
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
    
    logger.info("环境设置完成")


def adjust_config_for_memory(config: InferenceConfig, available_memory_gb: float):
    """根据可用内存调整配置"""
    
    logger.info(f"可用GPU内存: {available_memory_gb:.1f}GB")
    
    # 根据内存情况调整参数
    if available_memory_gb < 25:
        # 内存严重不足
        config.num_frames = min(config.num_frames, 25)
        config.height = min(config.height, 384)
        config.width = min(config.width, 640)
        logger.warning("内存不足，使用极小配置")
    elif available_memory_gb < 30:
        # 内存不足
        config.num_frames = min(config.num_frames, 49)
        config.height = min(config.height, 512)
        config.width = min(config.width, 768)
        logger.warning("内存有限，使用小配置")
    elif available_memory_gb < 35:
        # 内存一般
        config.num_frames = min(config.num_frames, 81)
        config.height = min(config.height, 576)
        config.width = min(config.width, 1024)
        logger.info("使用中等配置")
    else:
        # 内存充足
        logger.info("内存充足，使用原始配置")
    
    logger.info(f"调整后的配置 - 帧数: {config.num_frames}, 尺寸: {config.height}x{config.width}")
    return config


def main():
    parser = argparse.ArgumentParser(description='LTX-Video 视频处理推理')
    parser.add_argument('--video_dir', type=str, default='demo_video/airscape',
                        help='视频文件夹路径')
    parser.add_argument('--prompt_file', type=str, default='demo_video/prompt.txt',
                        help='Prompt文件路径')
    parser.add_argument('--output_dir', type=str, default='outputs_video',
                        help='输出文件夹路径')
    parser.add_argument('--model_path', type=str, default='LTXV_models/LTX-Video/ltxv-2b-0.9.6-dev-04-25.safetensors',
                        help='模型文件路径')
    parser.add_argument('--config_path', type=str, default='ltxv_2b_fixed_config.yaml',
                        help='配置文件路径')
    parser.add_argument('--single_video', type=str, default=None,
                        help='只处理单个视频文件（可选）')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    parser.add_argument('--num_frames', type=int, default=100,
                        help='生成的帧数')
    parser.add_argument('--height', type=int, default=448,
                        help='输出视频高度')
    parser.add_argument('--width', type=int, default=768,
                        help='输出视频宽度')
    parser.add_argument('--use_13b_model', action='store_true',
                        help='使用13B模型而不是2B模型')
    parser.add_argument('--disable_memory_adjustment', action='store_true',
                        help='禁用自动内存调整')
    parser.add_argument('--save_first_frame', action='store_true',
                        help='保存提取的第一帧图像')

    args = parser.parse_args()

    # 设置随机种子
    if args.seed is not None:
        torch.manual_seed(args.seed)
        random.seed(args.seed)
        np.random.seed(args.seed)
        logger.info(f"设置随机种子: {args.seed}")

    # 检查CUDA可用性
    if not torch.cuda.is_available():
        logger.error("CUDA不可用，无法使用GPU推理")
        return
    
    logger.info(f"使用GPU: {torch.cuda.get_device_name()}")
    
    # 如果使用13B模型，更新模型路径和配置
    if args.use_13b_model:
        args.model_path = 'LTXV_models/LTX-Video/ltxv-13b-0.9.7-dev.safetensors'
        args.config_path = 'ltxv_13b_fixed_config.yaml'
        logger.info("使用13B模型进行推理")

    # 设置环境
    setup_environment()

    # 检查文件
    if not os.path.exists(args.model_path):
        logger.error(f"模型文件不存在: {args.model_path}")
        return

    if not os.path.exists(args.config_path):
        logger.error(f"配置文件不存在: {args.config_path}")
        return

    # 显示初始GPU内存状态
    initial_memory = get_gpu_memory_info()
    logger.info(f"初始GPU内存状态: {initial_memory}")

    # 解析prompt文件
    prompts_dict = parse_prompt_txt(args.prompt_file)
    
    # 处理单个视频或批量处理
    if args.single_video:
        # 处理单个指定的视频文件
        video_path = os.path.join(args.video_dir, args.single_video)
        if not os.path.exists(video_path):
            logger.error(f"视频文件不存在: {video_path}")
            return
        
        video_files = [args.single_video]
        logger.info(f"处理单个视频: {args.single_video}")
    else:
        # 批量处理文件夹中的所有视频文件
        if not os.path.exists(args.video_dir):
            logger.error(f"视频文件夹不存在: {args.video_dir}")
            return
        
        video_files = [f for f in os.listdir(args.video_dir) if f.endswith('.mp4')]
        if not video_files:
            logger.error(f"在文件夹 {args.video_dir} 中未找到任何.mp4视频文件")
            return
        
        logger.info(f"在文件夹 {args.video_dir} 中找到 {len(video_files)} 个视频文件")

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 处理视频文件
    successful_count = 0
    failed_count = 0

    for i, video_filename in enumerate(video_files, 1):
        video_path = os.path.join(args.video_dir, video_filename)

        if not os.path.exists(video_path):
            logger.warning(f"跳过不存在的视频文件: {video_path}")
            failed_count += 1
            continue

        # 检查是否有对应的prompt
        if video_filename not in prompts_dict:
            logger.warning(f"跳过没有提示词的视频: {video_filename}")
            failed_count += 1
            continue

        prompt = prompts_dict[video_filename]

        # 生成输出目录（为每个视频创建单独的子目录）
        base_name = os.path.splitext(video_filename)[0]
        video_output_dir = os.path.join(args.output_dir, base_name)

        # 检查是否已经处理过（查看输出目录是否存在且包含视频文件）
        if os.path.exists(video_output_dir):
            existing_videos = [f for f in os.listdir(video_output_dir) if f.endswith('.mp4')]
            if existing_videos:
                logger.info(f"视频已处理，跳过: {video_filename} (输出: {existing_videos[0]})")
                continue

        logger.info(f"[{i}/{len(video_files)}] 处理视频: {video_filename}")
        logger.info(f"提示词: {prompt[:100]}...")

        try:
            # 提取第一帧
            if args.save_first_frame:
                frame_output_path = os.path.join(args.output_dir, f"{base_name}_first_frame.jpg")
                extract_first_frame(video_path, frame_output_path)
            else:
                extract_first_frame(video_path)  # 只是验证视频可读

            # 创建推理配置
            config = InferenceConfig(
                prompt=prompt,
                output_path=video_output_dir,  # 使用视频专用输出目录
                pipeline_config=args.config_path,
                height=args.height,
                width=args.width,
                num_frames=args.num_frames,
                conditioning_media_paths=[video_path],
                conditioning_start_frames=[0],
                seed=args.seed,
                offload_to_cpu=False  # 让官方代码自动决定
            )

            # 根据内存情况调整配置
            if not args.disable_memory_adjustment:
                available_memory = initial_memory.get("free", 0)
                config = adjust_config_for_memory(config, available_memory)

            logger.info("开始推理...")

            # 显示推理前的GPU内存状态
            pre_inference_memory = get_gpu_memory_info()
            logger.info(f"推理前GPU内存状态: {pre_inference_memory}")

            # 执行推理（使用官方代码）
            infer(config)

            logger.info(f"推理完成，输出保存到: {video_output_dir}")
            successful_count += 1

            # 显示推理后的GPU内存状态
            post_inference_memory = get_gpu_memory_info()
            logger.info(f"推理后GPU内存状态: {post_inference_memory}")

            # 清理内存
            gc.collect()
            torch.cuda.empty_cache()

        except torch.cuda.OutOfMemoryError as e:
            logger.error(f"CUDA内存不足: {e}")
            logger.error("建议尝试以下解决方案:")
            logger.error("1. 使用更小的分辨率和帧数")
            logger.error("2. 重启Python进程清理内存")
            logger.error("3. 检查其他进程是否占用GPU内存")

            # 显示错误时的内存状态
            error_memory = get_gpu_memory_info()
            logger.error(f"错误时GPU内存状态: {error_memory}")
            failed_count += 1

        except Exception as e:
            logger.error(f"推理过程中出错: {e}")
            import traceback
            traceback.print_exc()

            # 显示错误时的内存状态
            error_memory = get_gpu_memory_info()
            logger.error(f"错误时GPU内存状态: {error_memory}")
            failed_count += 1

    # 显示最终统计信息
    logger.info(f"处理完成! 成功: {successful_count}, 失败: {failed_count}, 总计: {len(video_files)}")


if __name__ == "__main__":
    main()

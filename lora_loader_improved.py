#!/usr/bin/env python3
"""
LTX-Video LoRA权重加载器 (改进版)

基于GitHub issue #16和LTX-Video-Trainer源码分析的最佳实践实现。

关键特性：
1. 多种LoRA加载策略，自动选择最佳方法
2. 完善的错误处理和验证机制
3. 兼容LTX-Video-Trainer框架
4. 支持单GPU A800环境优化

作者：AI Assistant
日期：2025-07-18
基于：GitHub issue #16 和 LTX-Video-Trainer源码分析
"""

import os
import sys
import torch
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import warnings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LTXVideoLoRALoader:
    """
    LTX-Video LoRA权重加载器
    
    提供多种LoRA加载策略，确保在不同环境下都能正常工作
    """
    
    def __init__(self, pipeline, device: str = "cuda"):
        """
        初始化LoRA加载器
        
        Args:
            pipeline: LTXConditionPipeline实例
            device: 计算设备
        """
        self.pipeline = pipeline
        self.device = device
        self._lora_loaded = False
        self._lora_fused = False
        self._lora_method = None
        self._current_lora_path = None
        
        # 检查可用的LoRA方法
        self._available_methods = self._check_available_methods()
        logger.info(f"可用的LoRA方法: {list(self._available_methods.keys())}")
    
    def _check_available_methods(self) -> Dict[str, bool]:
        """检查可用的LoRA加载方法"""
        methods = {}
        
        # 检查标准方法
        methods['standard'] = hasattr(self.pipeline, 'load_lora_weights')
        
        # 检查fuse方法
        methods['fuse'] = hasattr(self.pipeline, 'fuse_lora')
        
        # 检查适配器方法
        methods['adapters'] = hasattr(self.pipeline, 'set_adapters_scale')
        
        # 检查PEFT方法
        try:
            import peft
            methods['peft'] = hasattr(self.pipeline, 'transformer')
        except ImportError:
            methods['peft'] = False
        
        # 检查safetensors
        try:
            import safetensors
            methods['safetensors'] = True
        except ImportError:
            methods['safetensors'] = False
        
        return methods
    
    def load_lora_weights(self, lora_path: Path, lora_scale: float = 1.0) -> bool:
        """
        加载LoRA权重
        
        Args:
            lora_path: LoRA权重文件路径
            lora_scale: LoRA权重缩放因子
            
        Returns:
            bool: 加载是否成功
        """
        if not lora_path.exists():
            logger.error(f"LoRA文件不存在: {lora_path}")
            return False
        
        logger.info(f"开始加载LoRA权重: {lora_path.name}")
        
        # 如果已经加载了LoRA，先卸载
        if self._lora_loaded:
            logger.info("检测到已加载的LoRA，先卸载...")
            self.unload_lora_weights()
        
        # 尝试不同的加载方法
        success = False
        
        # 方法1：标准方法 + fuse（推荐）
        if self._available_methods.get('standard') and self._available_methods.get('fuse'):
            success = self._load_with_standard_fuse(lora_path, lora_scale)
            if success:
                self._lora_method = 'standard_fuse'
        
        # 方法2：标准方法 + 适配器
        if not success and self._available_methods.get('standard') and self._available_methods.get('adapters'):
            success = self._load_with_standard_adapters(lora_path, lora_scale)
            if success:
                self._lora_method = 'standard_adapters'
        
        # 方法3：PEFT方法（高级）
        if not success and self._available_methods.get('peft') and self._available_methods.get('safetensors'):
            success = self._load_with_peft(lora_path, lora_scale)
            if success:
                self._lora_method = 'peft'
        
        # 方法4：直接加载（最后尝试）
        if not success and self._available_methods.get('standard'):
            success = self._load_with_standard_only(lora_path)
            if success:
                self._lora_method = 'standard_only'
        
        if success:
            self._lora_loaded = True
            self._current_lora_path = lora_path
            logger.info(f"✅ LoRA权重加载成功，使用方法: {self._lora_method}")
        else:
            logger.error("❌ 所有LoRA加载方法都失败了")
        
        return success
    
    def _load_with_standard_fuse(self, lora_path: Path, lora_scale: float) -> bool:
        """使用标准方法 + fuse加载LoRA"""
        try:
            logger.info("尝试标准方法 + fuse...")
            
            # 加载LoRA权重
            self.pipeline.load_lora_weights(lora_path)
            
            # 融合LoRA权重
            self.pipeline.fuse_lora(lora_scale=lora_scale)
            self._lora_fused = True
            
            logger.info("✅ 标准方法 + fuse 成功")
            return True
            
        except Exception as e:
            logger.warning(f"标准方法 + fuse 失败: {e}")
            return False
    
    def _load_with_standard_adapters(self, lora_path: Path, lora_scale: float) -> bool:
        """使用标准方法 + 适配器加载LoRA"""
        try:
            logger.info("尝试标准方法 + 适配器...")
            
            # 加载LoRA权重
            self.pipeline.load_lora_weights(lora_path)
            
            # 设置适配器缩放
            self.pipeline.set_adapters_scale(lora_scale)
            
            logger.info("✅ 标准方法 + 适配器 成功")
            return True
            
        except Exception as e:
            logger.warning(f"标准方法 + 适配器 失败: {e}")
            return False
    
    def _load_with_peft(self, lora_path: Path, lora_scale: float) -> bool:
        """使用PEFT方法加载LoRA"""
        try:
            logger.info("尝试PEFT方法...")
            
            # 导入必要模块
            from peft import LoraConfig
            from safetensors.torch import load_file
            
            # 加载权重文件
            state_dict = load_file(lora_path)
            logger.info(f"加载了 {len(state_dict)} 个权重张量")
            
            # 检查是否需要设置适配器
            if not hasattr(self.pipeline.transformer, 'peft_config'):
                # 创建LoRA配置（使用LTX-Video-Trainer默认值）
                lora_config = LoraConfig(
                    r=64,
                    lora_alpha=64,
                    target_modules=["to_k", "to_q", "to_v", "to_out.0"],
                    lora_dropout=0.0,
                    init_lora_weights=True,
                )
                
                # 添加适配器
                self.pipeline.transformer.add_adapter(lora_config)
                logger.info("✅ 添加PEFT适配器")
            
            # 调整权重键名
            adjusted_state_dict = self._adjust_state_dict_keys(state_dict)
            
            # 加载权重
            missing_keys, unexpected_keys = self.pipeline.transformer.load_state_dict(
                adjusted_state_dict, strict=False
            )
            
            if unexpected_keys:
                logger.warning(f"发现 {len(unexpected_keys)} 个未预期的权重键")
            
            # 启用适配器
            if hasattr(self.pipeline.transformer, 'enable_adapters'):
                self.pipeline.transformer.enable_adapters()
            
            if hasattr(self.pipeline.transformer, 'set_adapter'):
                self.pipeline.transformer.set_adapter("default")
            
            logger.info("✅ PEFT方法 成功")
            return True
            
        except Exception as e:
            logger.warning(f"PEFT方法 失败: {e}")
            return False
    
    def _load_with_standard_only(self, lora_path: Path) -> bool:
        """仅使用标准方法加载LoRA"""
        try:
            logger.info("尝试仅标准方法...")
            
            # 只加载，不做额外处理
            self.pipeline.load_lora_weights(lora_path)
            
            logger.info("✅ 仅标准方法 成功")
            return True
            
        except Exception as e:
            logger.warning(f"仅标准方法 失败: {e}")
            return False
    
    def _adjust_state_dict_keys(self, state_dict: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """调整权重字典的键名以匹配PEFT格式"""
        adjusted_state_dict = {}
        
        for key, value in state_dict.items():
            # 移除transformer前缀
            if key.startswith("transformer."):
                key = key.replace("transformer.", "", 1)
            
            # 添加默认适配器名称
            if "lora_A" in key and ".default" not in key:
                key = key.replace("lora_A", "lora_A.default")
            elif "lora_B" in key and ".default" not in key:
                key = key.replace("lora_B", "lora_B.default")
            
            adjusted_state_dict[key] = value
        
        return adjusted_state_dict
    
    def unload_lora_weights(self) -> bool:
        """卸载LoRA权重"""
        if not self._lora_loaded:
            logger.info("没有加载的LoRA权重需要卸载")
            return True
        
        try:
            logger.info(f"卸载LoRA权重，使用方法: {self._lora_method}")
            
            # 根据加载方法选择卸载策略
            if self._lora_method == 'standard_fuse' and self._lora_fused:
                if hasattr(self.pipeline, 'unfuse_lora'):
                    self.pipeline.unfuse_lora()
                    self._lora_fused = False
                    logger.info("✅ LoRA权重已unfuse")
            
            # 标准卸载
            if hasattr(self.pipeline, 'unload_lora_weights'):
                self.pipeline.unload_lora_weights()
                logger.info("✅ 使用标准方法卸载LoRA")
            
            # PEFT适配器处理
            elif self._lora_method == 'peft':
                if hasattr(self.pipeline.transformer, 'disable_adapters'):
                    self.pipeline.transformer.disable_adapters()
                    logger.info("✅ 禁用PEFT适配器")
            
            self._lora_loaded = False
            self._lora_method = None
            self._current_lora_path = None
            
            return True
            
        except Exception as e:
            logger.error(f"卸载LoRA权重失败: {e}")
            return False
    
    def is_lora_loaded(self) -> bool:
        """检查是否已加载LoRA权重"""
        return self._lora_loaded
    
    def get_current_lora_info(self) -> Optional[Dict[str, Any]]:
        """获取当前LoRA信息"""
        if not self._lora_loaded:
            return None
        
        return {
            'path': str(self._current_lora_path),
            'method': self._lora_method,
            'fused': self._lora_fused,
        }
    
    def validate_lora_weights(self, lora_path: Path) -> Dict[str, Any]:
        """验证LoRA权重文件"""
        validation_result = {
            'valid': False,
            'file_exists': False,
            'file_size': 0,
            'num_tensors': 0,
            'tensor_info': {},
            'errors': []
        }
        
        try:
            # 检查文件存在
            if not lora_path.exists():
                validation_result['errors'].append(f"文件不存在: {lora_path}")
                return validation_result
            
            validation_result['file_exists'] = True
            validation_result['file_size'] = lora_path.stat().st_size
            
            # 尝试加载权重信息
            if self._available_methods.get('safetensors'):
                from safetensors.torch import load_file
                state_dict = load_file(lora_path)
                validation_result['num_tensors'] = len(state_dict)
                
                # 分析张量信息
                for key, tensor in state_dict.items():
                    validation_result['tensor_info'][key] = {
                        'shape': list(tensor.shape),
                        'dtype': str(tensor.dtype),
                        'device': str(tensor.device)
                    }
            
            validation_result['valid'] = True
            
        except Exception as e:
            validation_result['errors'].append(f"验证失败: {e}")
        
        return validation_result


# 使用示例和测试函数
def test_lora_loader():
    """测试LoRA加载器"""
    logger.info("开始测试LoRA加载器...")
    
    # 这里需要实际的pipeline实例
    # pipeline = LTXConditionPipeline.from_pretrained(...)
    # loader = LTXVideoLoRALoader(pipeline)
    
    # 测试验证功能
    # lora_path = Path("path/to/lora_weights.safetensors")
    # validation = loader.validate_lora_weights(lora_path)
    # print(f"验证结果: {validation}")
    
    logger.info("测试完成")


if __name__ == "__main__":
    test_lora_loader()

name: Ruff

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10"]
    steps:
    - name: Checkout repository and submodules
      uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install ruff==0.2.2 black==24.2.0
    - name: Analyzing the code with ruff
      run: |
        ruff $(git ls-files '*.py')
    - name: Verify that no Black changes are required
      run: |
        black --check $(git ls-files '*.py')

from transformers import HfArgumentParser

from ltx_video.inference import infer, InferenceConfig


def main():
    parser = HfArgumentParser(InferenceConfig)
    config = parser.parse_args_into_dataclasses()[0]
    infer(config=config)


if __name__ == "__main__":
    main()



# python inference.py --prompt "A white and orange kitten is yawning, with its mouth wide open, showing a hint of tiny teeth, eyes slightly squinted as if relaxed, with a soft and fluffy coat. Capture the natural, endearing moment of the yawn, making the kitten look approachable and charming." --conditioning_media_paths /data1/wzy/LTXV_ALL/demo_video/24poJOgl7m_small.jpg --conditioning_start_frames 0 --height 448 --width 768 --num_frames 109 --seed 42 --pipeline_config /data1/wzy/LTXV_ALL/LTX-Video/ltxv_2b_fixed_config.yaml
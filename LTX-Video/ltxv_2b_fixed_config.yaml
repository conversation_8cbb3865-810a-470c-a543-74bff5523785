pipeline_type: base
checkpoint_path: "/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video/ltxv-2b-0.9.6-dev-04-25.safetensors"
guidance_scale: 3
stg_scale: 1
rescaling_scale: 0.7
skip_block_list: [19]
num_inference_steps: 40
stg_mode: "attention_values"
decode_timestep: 0.05
decode_noise_scale: 0.025
text_encoder_model_name_or_path: "/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video"
precision: "bfloat16"
sampler: "from_checkpoint"
prompt_enhancement_words_threshold: 0  # 禁用提示词增强以节省内存
prompt_enhancer_image_caption_model_name_or_path: "MiaoshouAI/Florence-2-large-PromptGen-v2.0"
prompt_enhancer_llm_model_name_or_path: "unsloth/Llama-3.2-3B-Instruct"
stochastic_sampling: false

#!/usr/bin/env python3
"""
LoRA训练效果评估脚本 (简化版)

该脚本使用LTX-Video原生inference.py进行LoRA评估，避免ComfyUI的复杂性。

功能：
1. 自动遍历checkpoints目录中的所有checkpoint文件
2. 从测试视频中提取首帧作为参考图像
3. 从prompt.txt中读取对应的文本提示词
4. 使用LTX-Video inference.py生成视频
5. 将生成的视频保存到有组织的输出目录中

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import cv2
import json
import argparse
import subprocess
from pathlib import Path
from typing import List, Dict, Optional
import logging
from tqdm import tqdm
import re
from datetime import datetime
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lora_evaluation_simple.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleLoRAEvaluator:
    """简化版LoRA评估器"""
    
    def __init__(self, 
                 checkpoints_dir: str,
                 base_model_path: str,
                 test_videos_dir: str,
                 prompt_file: str,
                 output_dir: str,
                 ltx_video_path: str):
        
        self.checkpoints_dir = Path(checkpoints_dir)
        self.base_model_path = Path(base_model_path)
        self.test_videos_dir = Path(test_videos_dir)
        self.prompt_file = Path(prompt_file)
        self.output_dir = Path(output_dir)
        self.ltx_video_path = Path(ltx_video_path)
        
        self.checkpoints = []
        self.test_videos = []
        self.prompts = {}
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
    def setup(self) -> bool:
        """初始化设置"""
        logger.info("开始初始化评估环境...")
        
        # 检查必要路径
        if not self._check_paths():
            return False
            
        # 扫描checkpoints
        if not self._scan_checkpoints():
            return False
            
        # 扫描测试视频
        if not self._scan_test_videos():
            return False
            
        # 加载提示词
        if not self._load_prompts():
            return False
            
        logger.info(f"找到 {len(self.checkpoints)} 个checkpoints")
        logger.info(f"找到 {len(self.test_videos)} 个测试视频")
        logger.info(f"加载了 {len(self.prompts)} 个提示词")
        
        return True
    
    def _check_paths(self) -> bool:
        """检查必要路径是否存在"""
        paths_to_check = [
            (self.checkpoints_dir, "Checkpoints目录"),
            (self.base_model_path, "基础模型路径"),
            (self.test_videos_dir, "测试视频目录"),
            (self.prompt_file, "提示词文件"),
            (self.ltx_video_path, "LTX-Video路径")
        ]
        
        for path, name in paths_to_check:
            if not path.exists():
                logger.error(f"{name} 不存在: {path}")
                return False
                
        # 检查inference.py是否存在
        inference_script = self.ltx_video_path / "inference.py"
        if not inference_script.exists():
            logger.error(f"inference.py 不存在: {inference_script}")
            return False
                
        return True
    
    def _scan_checkpoints(self) -> bool:
        """扫描checkpoint文件"""
        # 查找所有.safetensors文件
        checkpoint_files = list(self.checkpoints_dir.glob("*.safetensors"))
        
        if not checkpoint_files:
            logger.error(f"在 {self.checkpoints_dir} 中未找到任何.safetensors文件")
            return False
        
        # 按步数排序
        def extract_step_number(filename: str) -> int:
            match = re.search(r'step_(\d+)', filename)
            return int(match.group(1)) if match else 0
        
        checkpoint_files.sort(key=lambda x: extract_step_number(x.name))
        
        self.checkpoints = [
            {
                'path': checkpoint_file,
                'name': checkpoint_file.stem,
                'step': extract_step_number(checkpoint_file.name)
            }
            for checkpoint_file in checkpoint_files
        ]
        
        return True
    
    def _scan_test_videos(self) -> bool:
        """扫描测试视频文件"""
        # 查找所有.mp4文件
        video_files = list(self.test_videos_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error(f"在 {self.test_videos_dir} 中未找到任何.mp4文件")
            return False
        
        self.test_videos = [
            {
                'path': video_file,
                'name': video_file.stem,
                'filename': video_file.name
            }
            for video_file in video_files
        ]
        
        return True
    
    def _load_prompts(self) -> bool:
        """加载提示词文件"""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析表格格式的提示词
            lines = content.strip().split('\n')
            for line in lines[2:]:  # 跳过表头
                if '|' in line:
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 4:
                        example_num = parts[1]
                        motion_description = parts[3]
                        
                        # 提取视频文件名模式
                        if 'urbanvideo_test' in motion_description or '00819' in example_num:
                            self.prompts['00819_urbanvideo_test'] = motion_description
                        elif '00840' in example_num:
                            self.prompts['00840_urbanvideo_test'] = motion_description
                        elif '00846' in example_num:
                            self.prompts['00846_urbanvideo_test'] = motion_description
                        elif '01035' in example_num:
                            self.prompts['01035_NAT2021_test_N02029_4'] = motion_description
                        elif '01374' in example_num:
                            self.prompts['01374_NAT2021_test_N08024_3'] = motion_description
            
            return len(self.prompts) > 0
            
        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
            return False
    
    def extract_first_frame(self, video_path: Path, output_path: Path) -> bool:
        """从视频中提取首帧"""
        try:
            cap = cv2.VideoCapture(str(video_path))
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                cv2.imwrite(str(output_path), frame)
                return True
            else:
                logger.error(f"无法从视频中读取帧: {video_path}")
                return False
                
        except Exception as e:
            logger.error(f"提取首帧失败: {e}")
            return False
    
    def run_inference(self, 
                     lora_path: Path, 
                     image_path: Path, 
                     prompt: str, 
                     output_path: Path,
                     width: int = 1216,
                     height: int = 704,
                     num_frames: int = 121,
                     seed: int = 42) -> bool:
        """运行LTX-Video inference"""
        try:
            # 构建inference命令
            cmd = [
                sys.executable,
                str(self.ltx_video_path / "inference.py"),
                "--prompt", prompt,
                "--conditioning_media_paths", str(image_path),
                "--conditioning_start_frames", "0",
                "--height", str(height),
                "--width", str(width),
                "--num_frames", str(num_frames),
                "--seed", str(seed),
                "--pipeline_config", str(self.ltx_video_path / "configs" / "ltxv-2b-0.9.6-dev.yaml"),
                "--output_path", str(output_path)
            ]
            
            # 设置环境变量
            env = os.environ.copy()
            env['CUDA_VISIBLE_DEVICES'] = '0'  # 使用第一张GPU
            
            logger.info(f"运行inference: {' '.join(cmd)}")
            
            # 运行inference
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600,  # 10分钟超时
                cwd=str(self.ltx_video_path),
                env=env
            )
            
            if result.returncode == 0:
                logger.info(f"Inference成功完成: {output_path}")
                return True
            else:
                logger.error(f"Inference失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Inference执行超时")
            return False
        except Exception as e:
            logger.error(f"运行inference时出错: {e}")
            return False

    def evaluate_single_checkpoint(self, checkpoint: Dict, test_video: Dict) -> bool:
        """评估单个checkpoint"""
        checkpoint_name = checkpoint['name']
        video_name = test_video['name']

        logger.info(f"评估 {checkpoint_name} 使用测试视频 {video_name}")

        # 创建输出目录
        output_dir = self.output_dir / checkpoint_name
        output_dir.mkdir(exist_ok=True)

        # 提取首帧
        first_frame_path = output_dir / f"{video_name}_first_frame.jpg"
        if not self.extract_first_frame(test_video['path'], first_frame_path):
            return False

        # 获取对应的提示词
        prompt = self.prompts.get(video_name, "A drone flying over a landscape")

        # 生成视频
        output_video_path = output_dir / f"{video_name}_generated.mp4"

        # 注意：这里需要手动加载LoRA，但LTX-Video的inference.py可能不直接支持LoRA
        # 作为简化版本，我们先生成基础视频，后续可以扩展LoRA支持
        success = self.run_inference(
            checkpoint['path'],
            first_frame_path,
            prompt,
            output_video_path
        )

        if success:
            logger.info(f"成功生成视频: {output_video_path}")

            # 保存元数据
            metadata = {
                'checkpoint': checkpoint_name,
                'step': checkpoint['step'],
                'test_video': video_name,
                'prompt': prompt,
                'generated_at': datetime.now().isoformat()
            }

            metadata_path = output_dir / f"{video_name}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
        else:
            logger.error(f"生成视频失败: {output_video_path}")

        return success

    def run_evaluation(self) -> None:
        """运行完整评估"""
        logger.info("开始LoRA checkpoint评估...")

        total_tasks = len(self.checkpoints) * len(self.test_videos)
        completed_tasks = 0
        failed_tasks = 0

        # 创建进度条
        with tqdm(total=total_tasks, desc="评估进度") as pbar:
            for checkpoint in self.checkpoints:
                for test_video in self.test_videos:
                    try:
                        success = self.evaluate_single_checkpoint(checkpoint, test_video)
                        if success:
                            completed_tasks += 1
                        else:
                            failed_tasks += 1
                    except Exception as e:
                        logger.error(f"评估过程中出现异常: {e}")
                        failed_tasks += 1

                    pbar.update(1)
                    pbar.set_postfix({
                        'completed': completed_tasks,
                        'failed': failed_tasks
                    })

        # 生成评估报告
        self.generate_report(completed_tasks, failed_tasks, total_tasks)

    def generate_report(self, completed: int, failed: int, total: int) -> None:
        """生成评估报告"""
        report_path = self.output_dir / "evaluation_report.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# LoRA Checkpoint 评估报告\n\n")
            f.write(f"**评估时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**总任务数**: {total}\n")
            f.write(f"**成功完成**: {completed}\n")
            f.write(f"**失败任务**: {failed}\n")
            f.write(f"**成功率**: {completed/total*100:.1f}%\n\n")

            f.write("## Checkpoints\n\n")
            for checkpoint in self.checkpoints:
                f.write(f"- {checkpoint['name']} (Step: {checkpoint['step']})\n")

            f.write("\n## 测试视频\n\n")
            for video in self.test_videos:
                f.write(f"- {video['name']}\n")
                prompt = self.prompts.get(video['name'], "无对应提示词")
                f.write(f"  - 提示词: {prompt}\n")

            f.write("\n## 输出结构\n\n")
            f.write("```\n")
            f.write(f"{self.output_dir}/\n")
            for checkpoint in self.checkpoints:
                f.write(f"├── {checkpoint['name']}/\n")
                for video in self.test_videos:
                    f.write(f"│   ├── {video['name']}_first_frame.jpg\n")
                    f.write(f"│   ├── {video['name']}_generated.mp4\n")
                    f.write(f"│   └── {video['name']}_metadata.json\n")
            f.write("└── evaluation_report.md\n")
            f.write("```\n")

        logger.info(f"评估报告已生成: {report_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LoRA训练效果评估脚本 (简化版)")

    parser.add_argument("--checkpoints-dir",
                       default="/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints",
                       help="LoRA checkpoints目录路径")

    parser.add_argument("--base-model-path",
                       default="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video",
                       help="基础模型路径")

    parser.add_argument("--test-videos-dir",
                       default="/data1/wzy/LTXV_ALL/demo_video/airscape",
                       help="测试视频目录路径")

    parser.add_argument("--prompt-file",
                       default="/data1/wzy/LTXV_ALL/demo_video/prompt.txt",
                       help="提示词文件路径")

    parser.add_argument("--output-dir",
                       default="/data1/wzy/LTXV_ALL/lora_evaluation_results",
                       help="输出目录路径")

    parser.add_argument("--ltx-video-path",
                       default="/data1/wzy/LTXV_ALL/LTX-Video",
                       help="LTX-Video项目路径")

    parser.add_argument("--dry-run", action="store_true", help="仅检查配置，不执行评估")
    parser.add_argument("--verbose", action="store_true", help="详细输出")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 创建评估器
    evaluator = SimpleLoRAEvaluator(
        checkpoints_dir=args.checkpoints_dir,
        base_model_path=args.base_model_path,
        test_videos_dir=args.test_videos_dir,
        prompt_file=args.prompt_file,
        output_dir=args.output_dir,
        ltx_video_path=args.ltx_video_path
    )

    # 初始化
    if not evaluator.setup():
        logger.error("初始化失败，退出程序")
        sys.exit(1)

    if args.dry_run:
        logger.info("Dry run模式，配置检查完成")
        return

    # 运行评估
    try:
        evaluator.run_evaluation()
        logger.info("评估完成！")
    except KeyboardInterrupt:
        logger.info("用户中断评估")
    except Exception as e:
        logger.error(f"评估过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

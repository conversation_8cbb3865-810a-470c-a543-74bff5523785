#!/usr/bin/env python3
"""
LoRA训练效果评估脚本 (改进版)

基于对LTX-Video-Trainer项目的深入分析，使用正确的LoRA加载机制。

关键改进：
1. 使用LTXConditionPipeline而非原生inference.py
2. 正确实现LoRA权重加载流程
3. 使用LTXVideoLoraLoaderMixin的功能
4. 符合LTX-Video-Trainer的官方推荐

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import cv2
import json
import argparse
import torch
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional
import logging
from tqdm import tqdm
import re
from datetime import datetime

# 配置日志（移到导入之前）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lora_evaluation_improved.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 添加LTX-Video-Trainer到Python路径
trainer_src_path = Path(__file__).parent / "LTX-Video-Trainer" / "src"
if trainer_src_path.exists():
    sys.path.insert(0, str(trainer_src_path))
    logger.info(f"添加LTX-Video-Trainer路径: {trainer_src_path}")
else:
    logger.warning(f"LTX-Video-Trainer源码路径不存在: {trainer_src_path}")

# 尝试导入必要的模块
LTXConditionPipeline = None
LTXVideoCondition = None
load_ltxv_components = None
LtxvModelVersion = None

try:
    from ltxv_trainer.ltxv_pipeline import LTXConditionPipeline, LTXVideoCondition
    logger.info("✅ 成功导入LTXConditionPipeline")
except ImportError as e:
    logger.error(f"❌ 导入LTXConditionPipeline失败: {e}")

try:
    from ltxv_trainer.model_loader import load_ltxv_components, LtxvModelVersion
    logger.info("✅ 成功导入model_loader")
except ImportError as e:
    logger.error(f"❌ 导入model_loader失败: {e}")

try:
    import PIL.Image
    logger.info("✅ 成功导入PIL")
except ImportError as e:
    logger.error(f"❌ 导入PIL失败: {e}")

try:
    import imageio
    logger.info("✅ 成功导入imageio")
except ImportError as e:
    logger.error(f"❌ 导入imageio失败: {e}")

# 检查关键模块是否成功导入
if LTXConditionPipeline is None or load_ltxv_components is None:
    logger.error("关键模块导入失败，无法继续执行")
    logger.error("请确保:")
    logger.error("1. LTX-Video-Trainer正确安装")
    logger.error("2. 当前目录下存在LTX-Video-Trainer文件夹")
    logger.error("3. 激活了正确的conda环境")
    sys.exit(1)

# 日志已在上面配置

class ImprovedLoRAEvaluator:
    """改进版LoRA评估器，使用正确的LTX-Video-Trainer API"""
    
    def __init__(self, 
                 checkpoints_dir: str,
                 base_model_path: str,
                 test_videos_dir: str,
                 prompt_file: str,
                 output_dir: str):
        
        self.checkpoints_dir = Path(checkpoints_dir)
        self.base_model_path = Path(base_model_path)
        self.test_videos_dir = Path(test_videos_dir)
        self.prompt_file = Path(prompt_file)
        self.output_dir = Path(output_dir)
        
        self.checkpoints = []
        self.test_videos = []
        self.prompts = {}
        self.pipeline = None
        self._lora_fused = False  # 跟踪LoRA融合状态
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 设备配置
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"使用设备: {self.device}")
        
    def setup(self) -> bool:
        """初始化设置"""
        logger.info("开始初始化改进版评估环境...")

        try:
            # 检查必要路径
            logger.info("步骤1: 检查必要路径...")
            if not self._check_paths():
                logger.error("路径检查失败")
                return False
            logger.info("✅ 路径检查通过")

            # 扫描checkpoints
            logger.info("步骤2: 扫描checkpoints...")
            if not self._scan_checkpoints():
                logger.error("Checkpoints扫描失败")
                return False
            logger.info("✅ Checkpoints扫描完成")

            # 扫描测试视频
            logger.info("步骤3: 扫描测试视频...")
            if not self._scan_test_videos():
                logger.error("测试视频扫描失败")
                return False
            logger.info("✅ 测试视频扫描完成")

            # 加载提示词
            logger.info("步骤4: 加载提示词...")
            if not self._load_prompts():
                logger.error("提示词加载失败")
                return False
            logger.info("✅ 提示词加载完成")

            # 初始化基础管道
            logger.info("步骤5: 初始化基础管道...")
            if not self._init_pipeline():
                logger.error("管道初始化失败")
                return False
            logger.info("✅ 管道初始化完成")

            logger.info(f"找到 {len(self.checkpoints)} 个checkpoints")
            logger.info(f"找到 {len(self.test_videos)} 个测试视频")
            logger.info(f"加载了 {len(self.prompts)} 个提示词")

            return True

        except Exception as e:
            logger.error(f"初始化过程中出现异常: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def _check_paths(self) -> bool:
        """检查必要路径是否存在"""
        paths_to_check = [
            (self.checkpoints_dir, "Checkpoints目录"),
            (self.base_model_path, "基础模型路径"),
            (self.test_videos_dir, "测试视频目录"),
            (self.prompt_file, "提示词文件")
        ]
        
        for path, name in paths_to_check:
            if not path.exists():
                logger.error(f"{name} 不存在: {path}")
                return False
                
        return True
    
    def _scan_checkpoints(self) -> bool:
        """扫描checkpoint文件"""
        # 查找所有.safetensors文件
        checkpoint_files = list(self.checkpoints_dir.glob("*.safetensors"))
        
        if not checkpoint_files:
            logger.error(f"在 {self.checkpoints_dir} 中未找到任何.safetensors文件")
            return False
        
        # 按步数排序
        def extract_step_number(filename: str) -> int:
            match = re.search(r'step_(\d+)', filename)
            return int(match.group(1)) if match else 0
        
        checkpoint_files.sort(key=lambda x: extract_step_number(x.name))
        
        self.checkpoints = [
            {
                'path': checkpoint_file,
                'name': checkpoint_file.stem,
                'step': extract_step_number(checkpoint_file.name)
            }
            for checkpoint_file in checkpoint_files
        ]
        
        return True
    
    def _scan_test_videos(self) -> bool:
        """扫描测试视频文件"""
        # 查找所有.mp4文件
        video_files = list(self.test_videos_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error(f"在 {self.test_videos_dir} 中未找到任何.mp4文件")
            return False
        
        self.test_videos = [
            {
                'path': video_file,
                'name': video_file.stem,
                'filename': video_file.name
            }
            for video_file in video_files
        ]
        
        return True
    
    def _load_prompts(self) -> bool:
        """加载提示词文件"""
        try:
            logger.info(f"正在加载提示词文件: {self.prompt_file}")
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()

            logger.info(f"文件内容长度: {len(content)} 字符")

            # 解析表格格式的提示词
            lines = content.strip().split('\n')
            logger.info(f"文件共有 {len(lines)} 行")

            for i, line in enumerate(lines):
                logger.debug(f"处理第{i+1}行: {line[:100]}...")

                if '|' in line and i >= 2:  # 跳过表头
                    parts = [part.strip() for part in line.split('|')]
                    logger.debug(f"分割后有 {len(parts)} 部分")

                    if len(parts) >= 4:
                        example_num = parts[1]
                        prediction_content = parts[2]  # 包含图片路径
                        motion_description = parts[3]

                        logger.debug(f"示例编号: {example_num}")
                        logger.debug(f"预测内容: {prediction_content[:50]}...")
                        logger.debug(f"运动描述: {motion_description[:50]}...")

                        # 从预测内容中提取文件名
                        video_filename = None
                        if '00819_urbanvideo_test' in prediction_content:
                            video_filename = '00819_urbanvideo_test'
                        elif '00840_urbanvideo_test' in prediction_content:
                            video_filename = '00840_urbanvideo_test'
                        elif '00846_urbanvideo_test' in prediction_content:
                            video_filename = '00846_urbanvideo_test'
                        elif '01035_NAT2021_test_N02029_4' in prediction_content:
                            video_filename = '01035_NAT2021_test_N02029_4'
                        elif '01374_NAT2021_test_N08024_3' in prediction_content:
                            video_filename = '01374_NAT2021_test_N08024_3'

                        if video_filename:
                            self.prompts[video_filename] = motion_description
                            logger.info(f"✅ 匹配{video_filename}: {motion_description[:50]}...")

            logger.info(f"成功加载 {len(self.prompts)} 个提示词")
            for key, value in self.prompts.items():
                logger.info(f"  {key}: {value[:50]}...")

            return len(self.prompts) > 0

        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def _init_pipeline(self) -> bool:
        """初始化LTXConditionPipeline"""
        try:
            logger.info("初始化LTXConditionPipeline...")

            # 设置离线模式环境变量
            os.environ["HF_HUB_OFFLINE"] = "1"
            logger.info("设置离线模式")

            # 加载模型组件
            # 使用本地模型路径，与训练时保持一致
            logger.info(f"从本地路径加载模型: {self.base_model_path}")
            components = load_ltxv_components(
                model_source=str(self.base_model_path),  # 使用本地路径
                load_text_encoder_in_8bit=False,  # 避免8bit导致的精度问题
                transformer_dtype=torch.bfloat16,  # LoRA训练时使用的精度
                vae_dtype=torch.bfloat16,
            )

            logger.info("✅ 模型组件加载成功")

            # 创建管道
            logger.info("创建LTXConditionPipeline...")
            self.pipeline = LTXConditionPipeline(
                vae=components.vae,
                text_encoder=components.text_encoder,
                tokenizer=components.tokenizer,
                transformer=components.transformer,
                scheduler=components.scheduler,
            )

            logger.info("✅ 管道创建成功")

            # 移动到设备
            logger.info(f"移动管道到设备: {self.device}")
            self.pipeline = self.pipeline.to(self.device)

            # 启用内存优化
            logger.info("启用内存优化...")
            if hasattr(self.pipeline, 'enable_model_cpu_offload'):
                self.pipeline.enable_model_cpu_offload()
                logger.info("✅ 启用CPU卸载")

            if hasattr(self.pipeline, 'enable_vae_tiling'):
                self.pipeline.enable_vae_tiling()
                logger.info("✅ 启用VAE分块")

            logger.info("LTXConditionPipeline初始化成功")
            return True

        except Exception as e:
            logger.error(f"初始化管道失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def extract_first_frame(self, video_path: Path, output_path: Path) -> bool:
        """从视频中提取首帧"""
        try:
            cap = cv2.VideoCapture(str(video_path))
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                cv2.imwrite(str(output_path), frame)
                return True
            else:
                logger.error(f"无法从视频中读取帧: {video_path}")
                return False
                
        except Exception as e:
            logger.error(f"提取首帧失败: {e}")
            return False

    def load_lora_weights(self, lora_path: Path) -> bool:
        """
        改进版LoRA权重加载方法
        基于LTX-Video-Trainer源码分析的最佳实践
        """
        try:
            logger.info(f"加载LoRA权重: {lora_path.name}")

            # 方法1：使用LTXVideoLoraLoaderMixin的标准方法（推荐）
            try:
                # 这是LTX-Video-Trainer推荐的标准方法
                self.pipeline.load_lora_weights(lora_path)

                # 应用LoRA权重 - 使用fuse_lora是最可靠的方法
                if hasattr(self.pipeline, 'fuse_lora'):
                    self.pipeline.fuse_lora()
                    logger.info("✅ 使用fuse_lora方法应用LoRA权重")
                    self._lora_fused = True  # 标记已融合，用于后续清理
                elif hasattr(self.pipeline, 'set_adapters_scale'):
                    self.pipeline.set_adapters_scale(1.0)
                    logger.info("✅ 使用set_adapters_scale设置LoRA权重比例为1.0")
                    self._lora_fused = False
                else:
                    logger.warning("未找到LoRA权重应用方法，使用默认设置")
                    self._lora_fused = False

                logger.info(f"✅ LoRA权重加载成功: {lora_path.name}")
                return True

            except Exception as e:
                logger.warning(f"标准方法加载失败，尝试高级方法: {e}")
                return self._load_lora_weights_advanced(lora_path)

        except Exception as e:
            logger.error(f"加载LoRA权重失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def _load_lora_weights_advanced(self, lora_path: Path) -> bool:
        """
        高级LoRA权重加载方法
        基于PEFT库的直接操作，适用于复杂情况
        """
        try:
            logger.info("使用高级方法加载LoRA权重...")

            # 导入必要的PEFT模块
            from peft import LoraConfig, get_peft_model_state_dict
            from safetensors.torch import load_file

            # 1. 加载LoRA权重文件
            logger.info(f"从文件加载LoRA权重: {lora_path}")
            state_dict = load_file(lora_path)
            logger.info(f"加载了 {len(state_dict)} 个权重张量")

            # 2. 检查是否需要设置LoRA适配器
            if not hasattr(self.pipeline.transformer, 'peft_config'):
                logger.info("设置LoRA适配器配置...")

                # 使用与训练时一致的LoRA配置
                lora_config = LoraConfig(
                    r=64,  # 与LTX-Video-Trainer默认配置一致
                    lora_alpha=64,
                    target_modules=["to_k", "to_q", "to_v", "to_out.0"],
                    lora_dropout=0.0,
                    init_lora_weights=True,
                )

                # 添加适配器到transformer
                self.pipeline.transformer.add_adapter(lora_config)
                logger.info("✅ LoRA适配器配置完成")

            # 3. 调整权重键名以匹配PEFT格式
            adjusted_state_dict = {}
            for key, value in state_dict.items():
                # 移除可能的transformer前缀
                if key.startswith("transformer."):
                    key = key.replace("transformer.", "", 1)

                # 确保LoRA权重有正确的适配器名称
                if "lora_A" in key and ".default" not in key:
                    key = key.replace("lora_A", "lora_A.default")
                elif "lora_B" in key and ".default" not in key:
                    key = key.replace("lora_B", "lora_B.default")

                adjusted_state_dict[key] = value

            # 4. 加载权重到transformer
            missing_keys, unexpected_keys = self.pipeline.transformer.load_state_dict(
                adjusted_state_dict, strict=False
            )

            if unexpected_keys:
                logger.warning(f"发现未预期的权重键: {len(unexpected_keys)} 个")
                logger.debug(f"未预期的键: {unexpected_keys[:5]}...")

            if missing_keys:
                logger.warning(f"缺少的权重键: {len(missing_keys)} 个")
                logger.debug(f"缺少的键: {missing_keys[:5]}...")

            # 5. 启用适配器
            if hasattr(self.pipeline.transformer, 'enable_adapters'):
                self.pipeline.transformer.enable_adapters()
                logger.info("✅ LoRA适配器已启用")

            # 6. 设置适配器
            if hasattr(self.pipeline.transformer, 'set_adapter'):
                self.pipeline.transformer.set_adapter("default")
                logger.info("✅ 设置默认适配器")

            # 7. 确保数据类型一致性
            if hasattr(self.pipeline, 'transformer'):
                target_dtype = torch.bfloat16  # LTX-Video-Trainer使用bfloat16
                if self.pipeline.transformer.dtype != target_dtype:
                    logger.info(f"调整transformer数据类型到 {target_dtype}")
                    self.pipeline.transformer = self.pipeline.transformer.to(dtype=target_dtype)

            self._lora_fused = False  # 高级方法不使用fuse
            logger.info("✅ 高级方法LoRA权重加载成功")
            return True

        except Exception as e:
            logger.error(f"高级方法加载LoRA权重失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def unload_lora_weights(self) -> bool:
        """
        改进版LoRA权重卸载方法
        根据加载方式选择合适的卸载策略
        """
        try:
            # 如果使用了fuse_lora，需要先unfuse
            if hasattr(self, '_lora_fused') and self._lora_fused:
                if hasattr(self.pipeline, 'unfuse_lora'):
                    self.pipeline.unfuse_lora()
                    logger.info("✅ LoRA权重已unfuse")
                    self._lora_fused = False

            # 尝试标准卸载方法
            if hasattr(self.pipeline, 'unload_lora_weights'):
                self.pipeline.unload_lora_weights()
                logger.info("✅ 使用标准方法卸载LoRA权重")

            # 如果使用了PEFT适配器，禁用它们
            elif hasattr(self.pipeline.transformer, 'disable_adapters'):
                self.pipeline.transformer.disable_adapters()
                logger.info("✅ 禁用PEFT适配器")

                # 可选：完全删除适配器（释放内存）
                if hasattr(self.pipeline.transformer, 'delete_adapters'):
                    self.pipeline.transformer.delete_adapters()
                    logger.info("✅ 删除PEFT适配器")

            return True

        except Exception as e:
            logger.error(f"卸载LoRA权重失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def run_inference(self,
                     image_path: Path,
                     prompt: str,
                     output_path: Path,
                     width: int = 1216,
                     height: int = 704,
                     num_frames: int = 121,
                     fps: int = 30,
                     seed: int = 42) -> bool:
        """运行LTXConditionPipeline推理"""
        try:
            # 加载条件图像
            condition_image = PIL.Image.open(image_path).convert("RGB")
            condition_image = condition_image.resize((width, height))

            # 创建视频条件
            video_condition = LTXVideoCondition(
                image=condition_image,
                frame_index=0,
            )

            # 设置随机种子
            generator = torch.Generator(device=self.device).manual_seed(seed)

            # 运行推理
            logger.info(f"开始推理，提示词: {prompt}")
            output = self.pipeline(
                prompt=prompt,
                conditions=[video_condition],  # 使用conditions参数
                width=width,
                height=height,
                num_frames=num_frames,
                generator=generator,
                guidance_scale=3.0,  # 与训练配置保持一致
                num_inference_steps=40,  # 标准步数
            )

            # 保存视频
            frames = output.frames[0]  # 获取生成的帧

            # 使用imageio保存视频
            if 'imageio' not in globals():
                logger.error("imageio模块未正确导入，无法保存视频")
                return False

            logger.info(f"保存视频，共{len(frames)}帧")
            writer = imageio.get_writer(str(output_path), fps=fps)

            for i, frame in enumerate(frames):
                # 将PIL图像转换为numpy数组
                if hasattr(frame, 'convert'):  # PIL图像
                    frame_array = np.array(frame.convert('RGB'))
                else:  # 已经是numpy数组
                    frame_array = frame

                writer.append_data(frame_array)

                if i % 10 == 0:  # 每10帧显示一次进度
                    logger.debug(f"保存帧 {i+1}/{len(frames)}")

            writer.close()

            logger.info(f"视频已保存到: {output_path}")
            return True

        except Exception as e:
            logger.error(f"推理失败: {e}")
            return False

    def evaluate_single_checkpoint(self, checkpoint: Dict, test_video: Dict) -> bool:
        """评估单个checkpoint"""
        checkpoint_name = checkpoint['name']
        video_name = test_video['name']

        logger.info(f"评估 {checkpoint_name} 使用测试视频 {video_name}")

        # 创建输出目录
        output_dir = self.output_dir / checkpoint_name
        output_dir.mkdir(exist_ok=True)

        # 提取首帧
        first_frame_path = output_dir / f"{video_name}_first_frame.jpg"
        if not self.extract_first_frame(test_video['path'], first_frame_path):
            return False

        # 获取对应的提示词
        prompt = self.prompts.get(video_name, "A drone flying over a landscape")

        # 加载LoRA权重
        if not self.load_lora_weights(checkpoint['path']):
            return False

        try:
            # 生成视频
            output_video_path = output_dir / f"{video_name}_generated.mp4"
            success = self.run_inference(
                first_frame_path,
                prompt,
                output_video_path
            )

            if success:
                logger.info(f"成功生成视频: {output_video_path}")

                # 保存元数据
                metadata = {
                    'checkpoint': checkpoint_name,
                    'step': checkpoint['step'],
                    'test_video': video_name,
                    'prompt': prompt,
                    'generated_at': datetime.now().isoformat(),
                    'model_config': {
                        'width': 1216,
                        'height': 704,
                        'num_frames': 121,
                        'guidance_scale': 3.0,
                        'num_inference_steps': 40
                    }
                }

                metadata_path = output_dir / f"{video_name}_metadata.json"
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False)
            else:
                logger.error(f"生成视频失败: {output_video_path}")

            return success
        finally:
            # 确保卸载LoRA权重，避免影响下一次评估
            self.unload_lora_weights()

    def run_evaluation(self) -> None:
        """运行完整评估"""
        logger.info("开始LoRA checkpoint评估...")

        total_tasks = len(self.checkpoints) * len(self.test_videos)
        completed_tasks = 0
        failed_tasks = 0

        # 创建进度条
        with tqdm(total=total_tasks, desc="评估进度") as pbar:
            for checkpoint in self.checkpoints:
                for test_video in self.test_videos:
                    try:
                        success = self.evaluate_single_checkpoint(checkpoint, test_video)
                        if success:
                            completed_tasks += 1
                        else:
                            failed_tasks += 1
                    except Exception as e:
                        logger.error(f"评估过程中出现异常: {e}")
                        failed_tasks += 1

                    pbar.update(1)
                    pbar.set_postfix({
                        'completed': completed_tasks,
                        'failed': failed_tasks
                    })

        # 生成评估报告
        self.generate_report(completed_tasks, failed_tasks, total_tasks)

    def generate_report(self, completed: int, failed: int, total: int) -> None:
        """生成评估报告"""
        report_path = self.output_dir / "evaluation_report.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# LoRA Checkpoint 评估报告 (改进版)\n\n")
            f.write(f"**评估时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**使用管道**: LTXConditionPipeline (官方推荐)\n")
            f.write(f"**LoRA加载方式**: LTXVideoLoraLoaderMixin\n\n")
            f.write(f"**总任务数**: {total}\n")
            f.write(f"**成功完成**: {completed}\n")
            f.write(f"**失败任务**: {failed}\n")
            f.write(f"**成功率**: {completed/total*100:.1f}%\n\n")

            f.write("## Checkpoints\n\n")
            for checkpoint in self.checkpoints:
                f.write(f"- {checkpoint['name']} (Step: {checkpoint['step']})\n")

            f.write("\n## 测试视频\n\n")
            for video in self.test_videos:
                f.write(f"- {video['name']}\n")
                prompt = self.prompts.get(video['name'], "无对应提示词")
                f.write(f"  - 提示词: {prompt}\n")

            f.write("\n## 生成配置\n\n")
            f.write("- **分辨率**: 1216x704\n")
            f.write("- **帧数**: 121\n")
            f.write("- **引导比例**: 3.0\n")
            f.write("- **推理步数**: 40\n")
            f.write("- **精度**: bfloat16\n")

            f.write("\n## 输出结构\n\n")
            f.write("```\n")
            f.write(f"{self.output_dir}/\n")
            for checkpoint in self.checkpoints:
                f.write(f"├── {checkpoint['name']}/\n")
                for video in self.test_videos:
                    f.write(f"│   ├── {video['name']}_first_frame.jpg\n")
                    f.write(f"│   ├── {video['name']}_generated.mp4\n")
                    f.write(f"│   └── {video['name']}_metadata.json\n")
            f.write("└── evaluation_report.md\n")
            f.write("```\n")

        logger.info(f"评估报告已生成: {report_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LoRA训练效果评估脚本 (改进版)")

    parser.add_argument("--checkpoints-dir",
                       default="/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints",
                       help="LoRA checkpoints目录路径")

    parser.add_argument("--base-model-path",
                       default="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video",
                       help="基础模型路径")

    parser.add_argument("--test-videos-dir",
                       default="/data1/wzy/LTXV_ALL/demo_video/airscape",
                       help="测试视频目录路径")

    parser.add_argument("--prompt-file",
                       default="/data1/wzy/LTXV_ALL/demo_video/prompt.txt",
                       help="提示词文件路径")

    parser.add_argument("--output-dir",
                       default="/data1/wzy/LTXV_ALL/lora_evaluation_results_improved",
                       help="输出目录路径")

    parser.add_argument("--dry-run", action="store_true", help="仅检查配置，不执行评估")
    parser.add_argument("--verbose", action="store_true", help="详细输出")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 创建评估器
    evaluator = ImprovedLoRAEvaluator(
        checkpoints_dir=args.checkpoints_dir,
        base_model_path=args.base_model_path,
        test_videos_dir=args.test_videos_dir,
        prompt_file=args.prompt_file,
        output_dir=args.output_dir
    )

    # 初始化
    if not evaluator.setup():
        logger.error("初始化失败，退出程序")
        sys.exit(1)

    if args.dry_run:
        logger.info("Dry run模式，配置检查完成")
        logger.info("改进版脚本使用LTXConditionPipeline和正确的LoRA加载机制")
        return

    # 运行评估
    try:
        evaluator.run_evaluation()
        logger.info("评估完成！")
    except KeyboardInterrupt:
        logger.info("用户中断评估")
    except Exception as e:
        logger.error(f"评估过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

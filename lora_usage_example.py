#!/usr/bin/env python3
"""
LTX-Video LoRA加载器使用示例

展示如何在实际项目中使用改进的LoRA加载器，
基于GitHub issue #16的需求和LTX-Video-Trainer最佳实践。

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import torch
import numpy as np
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加LTX-Video-Trainer到Python路径
trainer_src_path = Path(__file__).parent / "LTX-Video-Trainer" / "src"
if trainer_src_path.exists():
    sys.path.insert(0, str(trainer_src_path))
    logger.info(f"添加LTX-Video-Trainer路径: {trainer_src_path}")

# 导入必要模块
try:
    from ltxv_trainer.ltxv_pipeline import LTXConditionPipeline, LTXVideoCondition
    from ltxv_trainer.model_loader import load_ltxv_components
    from lora_loader_improved import LTXVideoLoRALoader
    import PIL.Image
    import imageio
    logger.info("✅ 成功导入所有必要模块")
except ImportError as e:
    logger.error(f"❌ 导入模块失败: {e}")
    logger.error("请确保:")
    logger.error("1. LTX-Video-Trainer正确安装")
    logger.error("2. 当前目录下存在LTX-Video-Trainer文件夹")
    logger.error("3. 激活了正确的conda环境")
    sys.exit(1)


class LTXVideoWithLoRA:
    """
    集成LoRA功能的LTX-Video推理类
    
    展示如何正确使用改进的LoRA加载器
    """
    
    def __init__(self, base_model_path: str, device: str = "cuda"):
        """
        初始化
        
        Args:
            base_model_path: 基础模型路径
            device: 计算设备
        """
        self.base_model_path = Path(base_model_path)
        self.device = device
        self.pipeline = None
        self.lora_loader = None
        
        logger.info(f"初始化LTX-Video，基础模型: {base_model_path}")
        logger.info(f"使用设备: {device}")
    
    def setup(self) -> bool:
        """设置模型和管道"""
        try:
            logger.info("开始设置LTX-Video管道...")
            
            # 设置离线模式（避免下载）
            os.environ["HF_HUB_OFFLINE"] = "1"
            
            # 加载模型组件
            logger.info("加载模型组件...")
            components = load_ltxv_components(
                model_source=str(self.base_model_path),
                load_text_encoder_in_8bit=False,  # A800可以处理
                transformer_dtype=torch.bfloat16,  # 与训练一致
                vae_dtype=torch.bfloat16,
            )
            
            # 创建管道
            logger.info("创建LTXConditionPipeline...")
            self.pipeline = LTXConditionPipeline(**components)
            
            # 启用内存优化
            self.pipeline.enable_model_cpu_offload()
            self.pipeline.enable_vae_tiling()
            
            # 移动到设备
            self.pipeline = self.pipeline.to(self.device)
            
            # 创建LoRA加载器
            logger.info("创建LoRA加载器...")
            self.lora_loader = LTXVideoLoRALoader(self.pipeline, self.device)
            
            logger.info("✅ LTX-Video管道设置完成")
            return True
            
        except Exception as e:
            logger.error(f"设置管道失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def load_lora(self, lora_path: str, lora_scale: float = 1.0) -> bool:
        """
        加载LoRA权重
        
        Args:
            lora_path: LoRA权重文件路径
            lora_scale: LoRA权重缩放因子
            
        Returns:
            bool: 加载是否成功
        """
        if self.lora_loader is None:
            logger.error("LoRA加载器未初始化，请先调用setup()")
            return False
        
        lora_path = Path(lora_path)
        
        # 验证LoRA文件
        logger.info("验证LoRA权重文件...")
        validation = self.lora_loader.validate_lora_weights(lora_path)
        
        if not validation['valid']:
            logger.error(f"LoRA文件验证失败: {validation['errors']}")
            return False
        
        logger.info(f"LoRA文件验证通过:")
        logger.info(f"  - 文件大小: {validation['file_size'] / 1024 / 1024:.1f} MB")
        logger.info(f"  - 张量数量: {validation['num_tensors']}")
        
        # 加载LoRA权重
        success = self.lora_loader.load_lora_weights(lora_path, lora_scale)
        
        if success:
            lora_info = self.lora_loader.get_current_lora_info()
            logger.info(f"✅ LoRA加载成功: {lora_info}")
        
        return success
    
    def unload_lora(self) -> bool:
        """卸载LoRA权重"""
        if self.lora_loader is None:
            return True
        
        return self.lora_loader.unload_lora_weights()
    
    def generate_video(self, 
                      image_path: str,
                      prompt: str,
                      output_path: str,
                      width: int = 1216,
                      height: int = 704,
                      num_frames: int = 121,
                      fps: int = 30,
                      seed: int = 42) -> bool:
        """
        生成视频
        
        Args:
            image_path: 条件图像路径
            prompt: 文本提示词
            output_path: 输出视频路径
            width: 视频宽度
            height: 视频高度
            num_frames: 帧数
            fps: 帧率
            seed: 随机种子
            
        Returns:
            bool: 生成是否成功
        """
        if self.pipeline is None:
            logger.error("管道未初始化，请先调用setup()")
            return False
        
        try:
            logger.info(f"开始生成视频...")
            logger.info(f"  - 条件图像: {image_path}")
            logger.info(f"  - 提示词: {prompt}")
            logger.info(f"  - 输出路径: {output_path}")
            logger.info(f"  - 分辨率: {width}x{height}")
            logger.info(f"  - 帧数: {num_frames}")
            
            # 加载条件图像
            condition_image = PIL.Image.open(image_path).convert("RGB")
            condition_image = condition_image.resize((width, height))
            
            # 创建视频条件
            video_condition = LTXVideoCondition(
                image=condition_image,
                frame_index=0,
            )
            
            # 设置随机种子
            torch.manual_seed(seed)
            
            # 生成视频
            with torch.no_grad():
                output = self.pipeline(
                    prompt=prompt,
                    conditions=[video_condition],
                    width=width,
                    height=height,
                    num_frames=num_frames,
                    guidance_scale=3.0,
                    num_inference_steps=40,
                    generator=torch.Generator(device=self.device).manual_seed(seed)
                )
            
            # 保存视频
            frames = output.frames[0]
            writer = imageio.get_writer(str(output_path), fps=fps)
            
            for frame in frames:
                if hasattr(frame, 'convert'):
                    frame_array = np.array(frame.convert('RGB'))
                else:
                    frame_array = frame
                writer.append_data(frame_array)
            
            writer.close()
            
            logger.info(f"✅ 视频生成成功: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"生成视频失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False


def main():
    """主函数 - 使用示例"""
    logger.info("=== LTX-Video LoRA加载器使用示例 ===")
    
    # 配置路径（请根据实际情况修改）
    base_model_path = "/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video"
    lora_path = "/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints/lora_weights_step_03000.safetensors"
    test_image = "/data1/wzy/LTXV_ALL/demo_video/airscape/00819_urbanvideo_test_first_frame.jpg"
    output_video = "/data1/wzy/LTXV_ALL/test_lora_output.mp4"
    
    # 检查路径
    if not Path(base_model_path).exists():
        logger.error(f"基础模型路径不存在: {base_model_path}")
        return
    
    if not Path(lora_path).exists():
        logger.error(f"LoRA权重路径不存在: {lora_path}")
        return
    
    if not Path(test_image).exists():
        logger.error(f"测试图像路径不存在: {test_image}")
        return
    
    # 创建LTX-Video实例
    ltx_video = LTXVideoWithLoRA(base_model_path)
    
    # 设置管道
    if not ltx_video.setup():
        logger.error("管道设置失败")
        return
    
    # 测试1：无LoRA生成
    logger.info("\n=== 测试1：无LoRA生成 ===")
    success = ltx_video.generate_video(
        image_path=test_image,
        prompt="A drone shot of a city street with cars and buildings, urban landscape, aerial view",
        output_path=output_video.replace(".mp4", "_no_lora.mp4"),
        seed=42
    )
    
    if not success:
        logger.error("无LoRA生成失败")
        return
    
    # 测试2：加载LoRA并生成
    logger.info("\n=== 测试2：加载LoRA并生成 ===")
    
    # 加载LoRA
    if not ltx_video.load_lora(lora_path, lora_scale=1.0):
        logger.error("LoRA加载失败")
        return
    
    # 生成视频
    success = ltx_video.generate_video(
        image_path=test_image,
        prompt="A drone shot of a city street with cars and buildings, urban landscape, aerial view",
        output_path=output_video.replace(".mp4", "_with_lora.mp4"),
        seed=42
    )
    
    if not success:
        logger.error("LoRA生成失败")
        return
    
    # 卸载LoRA
    ltx_video.unload_lora()
    
    logger.info("\n✅ 所有测试完成！")
    logger.info("请检查生成的视频文件，比较LoRA的效果。")


if __name__ == "__main__":
    main()

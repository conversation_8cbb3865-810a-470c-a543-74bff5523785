# LTX-Video 首帧输入推理 + 多GPU并行推理

本项目实现了两个主要功能：
1. **首帧输入推理**：修改原始推理脚本，使其只使用视频的第一帧作为条件，而不是整个视频
2. **多GPU并行推理**：实现多GPU并行处理，提高推理效率

## 文件说明

### 核心文件
- `ltxv_inference.py` - 修改后的单GPU推理脚本（支持首帧输入）
- `ltxv_multi_gpu_inference.py` - 多GPU并行推理脚本
- `test_ltxv_inference.py` - 测试脚本

### 主要修改

#### ltxv_inference.py 的修改
1. **添加首帧提取功能**：
   - 新增 `extract_first_frame()` 函数
   - 使用 OpenCV 提取视频第一帧并保存为图像
   
2. **修改推理逻辑**：
   - 创建临时目录存储提取的首帧
   - 使用首帧图像而不是完整视频作为 `conditioning_media_paths`
   - 推理完成后自动清理临时文件

3. **支持多种文件格式**：
   - 支持 `_test.mp4` 和 `_BEST.mp4` 等不同命名格式

## 使用方法

### 1. 单GPU首帧推理

```bash
# 处理单个视频
python ltxv_inference.py \
    --video_dir /data1/wzy/LTXV_ALL/test_val \
    --prompts_csv /data1/wzy/LTXV_ALL/test_val/test_0730.csv \
    --single_video 00800_urbanvideo_test.mp4 \
    --output_dir /data1/wzy/LTXV_ALL/Test_result \
    --num_frames 121 \
    --height 480 \
    --width 854

# 批量处理所有视频
python ltxv_inference.py \
    --video_dir /data1/wzy/LTXV_ALL/test_val \
    --prompts_csv /data1/wzy/LTXV_ALL/test_val/test_0730.csv \
    --output_dir /data1/wzy/LTXV_ALL/Test_result
```

### 2. 多GPU并行推理

```bash
# 自动检测可用GPU并并行推理
python ltxv_multi_gpu_inference.py \
    --video_dir /data1/wzy/LTXV_ALL/test_val \
    --prompts_csv /data1/wzy/LTXV_ALL/test_val/test_0730.csv \
    --output_dir /data1/wzy/LTXV_ALL/Test_result_multi_gpu \
    --num_frames 121 \
    --height 480 \
    --width 854

# 自定义GPU选择条件
python ltxv_multi_gpu_inference.py \
    --video_dir /data1/wzy/LTXV_ALL/test_val \
    --prompts_csv /data1/wzy/LTXV_ALL/test_val/test_0730.csv \
    --output_dir /data1/wzy/LTXV_ALL/Test_result_multi_gpu \
    --min_free_memory 15000 \
    --max_usage_percent 10
```

### 3. 运行测试

```bash
# 测试单GPU和多GPU推理功能
python test_ltxv_inference.py
```

## 参数说明

### 通用参数
- `--video_dir`: 输入视频文件夹路径
- `--prompts_csv`: 提示词CSV文件路径
- `--output_dir`: 输出文件夹路径
- `--model_path`: 模型文件路径
- `--config_path`: 配置文件路径
- `--num_frames`: 生成视频帧数
- `--height`: 输出视频高度
- `--width`: 输出视频宽度
- `--seed`: 随机种子

### 多GPU专用参数
- `--min_free_memory`: GPU最小空闲内存要求(MB)，默认10000
- `--max_usage_percent`: GPU最大使用率要求(%)，默认20

## 功能特性

### 首帧输入推理
- ✅ 自动提取视频第一帧作为条件图像
- ✅ 避免读取整个视频，节省内存和时间
- ✅ 支持多种视频格式和命名规则
- ✅ 自动清理临时文件

### 多GPU并行推理
- ✅ 自动检测可用GPU（基于内存使用情况）
- ✅ 智能任务分配，确保负载均衡
- ✅ 独立进程处理，避免GPU间干扰
- ✅ 实时进度监控
- ✅ 完善的错误处理和日志记录

### 输出文件命名
- 单GPU推理：`{image_id}_generated_fixed.mp4`
- 多GPU推理：`{image_id}_test.mp4`

## 目录结构

```
/data1/wzy/LTXV_ALL/
├── ltxv_inference.py                 # 修改后的单GPU推理脚本
├── ltxv_multi_gpu_inference.py       # 多GPU并行推理脚本
├── test_ltxv_inference.py            # 测试脚本
├── test_val/                         # 输入视频目录
│   ├── *.mp4                        # 测试视频文件
│   └── test_0730.csv                # 提示词文件
├── Test_result/                      # 单GPU输出目录
└── Test_result_multi_gpu/            # 多GPU输出目录
    ├── gpu_0/                       # GPU 0 的输出
    ├── gpu_1/                       # GPU 1 的输出
    └── ...
```

## 性能优化

### 内存优化
- 自动根据GPU内存调整推理参数
- 推理完成后立即清理临时文件
- 支持禁用自动内存调整

### 并行优化
- 多进程并行，充分利用多GPU资源
- 智能任务分配，避免GPU负载不均
- 独立的日志记录，便于调试

## 故障排除

### 常见问题
1. **GPU内存不足**：
   - 降低 `num_frames`、`height`、`width` 参数
   - 检查其他进程是否占用GPU内存

2. **没有可用GPU**：
   - 调整 `--min_free_memory` 和 `--max_usage_percent` 参数
   - 使用 `nvidia-smi` 检查GPU状态

3. **提示词匹配失败**：
   - 检查CSV文件编码和格式
   - 确认视频文件名与CSV中的image_id匹配

### 已修复的问题
4. **"总共有0个有效任务"问题** ✅ **已修复**：
   - **问题原因**：CSV文件中的image_id是完整文件名（如`00829_urbanvideo_test.mp4`），但原始的`extract_image_id()`函数会去掉`.mp4`后缀，导致匹配失败
   - **修复方案**：修改`extract_image_id()`函数直接返回完整文件名
   - **修复效果**：任务匹配成功率从0%提升到100%（300/300）
   - **影响版本**：v1.0修复版本已解决此问题

### 日志文件
- `ltxv_inference_fixed.log` - 单GPU推理日志
- `ltxv_multi_gpu_inference.log` - 多GPU推理日志
- `test_ltxv_inference.log` - 测试日志

## 技术实现

### 首帧提取
使用OpenCV的VideoCapture读取视频第一帧：
```python
cap = cv2.VideoCapture(video_path)
ret, frame = cap.read()
cv2.imwrite(output_path, frame)
```

### GPU检测
使用nvidia-smi命令获取GPU状态：
```python
subprocess.run(['nvidia-smi', '--query-gpu=index,memory.used,memory.total,memory.free', 
               '--format=csv,noheader,nounits'])
```

### 多进程并行
使用Python multiprocessing库实现：
```python
process = mp.Process(target=run_single_gpu_inference, args=(gpu_id, tasks, args, i))
process.start()
```

## 注意事项

1. **环境要求**：
   - 需要安装OpenCV：`pip install opencv-python`
   - 需要CUDA环境和nvidia-smi工具

2. **文件权限**：
   - 确保对输入和输出目录有读写权限

3. **资源管理**：
   - 多GPU推理会占用更多系统资源
   - 建议在推理前检查系统负载

4. **兼容性**：
   - 基于原始ltxv_inference.py修改，保持API兼容性
   - 支持所有原有的命令行参数

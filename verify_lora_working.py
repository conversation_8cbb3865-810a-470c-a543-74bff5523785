#!/usr/bin/env python3
"""
LoRA权重应用验证脚本

验证原始的LoRA加载方法确实有效，并提供简单的质量对比。

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import logging
from pathlib import Path
import torch
import cv2
import PIL.Image
import numpy as np
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加LTX-Video-Trainer到Python路径
trainer_src_path = Path(__file__).parent / "LTX-Video-Trainer" / "src"
if trainer_src_path.exists():
    sys.path.insert(0, str(trainer_src_path))

try:
    from ltxv_trainer.ltxv_pipeline import LTXConditionPipeline, LTXVideoCondition
    from ltxv_trainer.model_loader import load_ltxv_components
    import imageio
    logger.info("✅ 成功导入所有必要模块")
except ImportError as e:
    logger.error(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def verify_lora_effectiveness():
    """验证LoRA权重是否有效应用"""
    
    # 配置路径
    base_model_path = "/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video"
    test_video_path = "/data1/wzy/LTXV_ALL/demo_video/airscape/00819_urbanvideo_test.mp4"
    lora_checkpoint = "/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints/lora_weights_step_03000.safetensors"
    output_dir = Path("/data1/wzy/LTXV_ALL/lora_verification")
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 测试提示词
    test_prompt = "A drone shot of a city street with cars and buildings, urban landscape, aerial view"
    
    logger.info("开始LoRA权重应用验证...")
    
    # 初始化管道
    logger.info("初始化基础管道...")
    os.environ["HF_HUB_OFFLINE"] = "1"
    
    components = load_ltxv_components(
        model_source=str(base_model_path),
        load_text_encoder_in_8bit=False,
        transformer_dtype=torch.bfloat16,
        vae_dtype=torch.bfloat16,
    )
    
    pipeline = LTXConditionPipeline(
        vae=components.vae,
        text_encoder=components.text_encoder,
        tokenizer=components.tokenizer,
        transformer=components.transformer,
        scheduler=components.scheduler,
    )
    
    pipeline = pipeline.to("cuda")
    
    # 启用内存优化
    if hasattr(pipeline, 'enable_model_cpu_offload'):
        pipeline.enable_model_cpu_offload()
    if hasattr(pipeline, 'enable_vae_tiling'):
        pipeline.enable_vae_tiling()
    
    # 获取条件图像
    cap = cv2.VideoCapture(str(test_video_path))
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        raise ValueError(f"无法读取视频第一帧: {test_video_path}")
    
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    condition_image = PIL.Image.fromarray(frame_rgb)
    
    # 创建视频条件
    video_condition = LTXVideoCondition(
        image=condition_image,
        frame_index=0,
    )
    
    # 设置推理参数
    width, height = 512, 320
    num_frames = 121
    fps = 25
    generator = torch.Generator(device="cuda").manual_seed(42)
    
    # 测试1：基础模型（无LoRA）
    logger.info("=== 测试1：基础模型（无LoRA）===")
    start_time = datetime.now()
    
    output_baseline = pipeline(
        prompt=test_prompt,
        conditions=[video_condition],
        width=width,
        height=height,
        num_frames=num_frames,
        generator=generator,
        guidance_scale=3.0,
        num_inference_steps=40,
    )
    
    baseline_time = (datetime.now() - start_time).total_seconds()
    
    # 保存基础模型视频
    baseline_path = output_dir / "baseline_no_lora.mp4"
    frames = output_baseline.frames[0]
    writer = imageio.get_writer(str(baseline_path), fps=fps)
    for frame in frames:
        if hasattr(frame, 'convert'):
            frame_array = np.array(frame.convert('RGB'))
        else:
            frame_array = frame
        writer.append_data(frame_array)
    writer.close()
    
    logger.info(f"✅ 基础模型视频生成完成，耗时: {baseline_time:.1f}秒")
    
    # 测试2：应用LoRA权重
    logger.info("=== 测试2：应用LoRA权重 ===")
    
    # 加载LoRA权重（使用原始方法）
    pipeline.load_lora_weights(lora_checkpoint)
    if hasattr(pipeline, 'fuse_lora'):
        pipeline.fuse_lora()
        logger.info("✅ LoRA权重已融合")
    
    # 重新设置随机种子确保可比性
    generator = torch.Generator(device="cuda").manual_seed(42)
    
    start_time = datetime.now()
    
    output_lora = pipeline(
        prompt=test_prompt,
        conditions=[video_condition],
        width=width,
        height=height,
        num_frames=num_frames,
        generator=generator,
        guidance_scale=3.0,
        num_inference_steps=40,
    )
    
    lora_time = (datetime.now() - start_time).total_seconds()
    
    # 保存LoRA视频
    lora_path = output_dir / "with_lora.mp4"
    frames = output_lora.frames[0]
    writer = imageio.get_writer(str(lora_path), fps=fps)
    for frame in frames:
        if hasattr(frame, 'convert'):
            frame_array = np.array(frame.convert('RGB'))
        else:
            frame_array = frame
        writer.append_data(frame_array)
    writer.close()
    
    logger.info(f"✅ LoRA视频生成完成，耗时: {lora_time:.1f}秒")
    
    # 简单的差异分析
    logger.info("=== 差异分析 ===")
    
    # 计算文件大小差异
    baseline_size = baseline_path.stat().st_size
    lora_size = lora_path.stat().st_size
    size_diff = lora_size - baseline_size
    
    logger.info(f"基础模型视频大小: {baseline_size:,} bytes")
    logger.info(f"LoRA模型视频大小: {lora_size:,} bytes")
    logger.info(f"大小差异: {size_diff:+,} bytes ({size_diff/baseline_size*100:+.1f}%)")
    
    # 计算推理时间差异
    time_diff = lora_time - baseline_time
    logger.info(f"基础模型推理时间: {baseline_time:.1f}秒")
    logger.info(f"LoRA模型推理时间: {lora_time:.1f}秒")
    logger.info(f"时间差异: {time_diff:+.1f}秒 ({time_diff/baseline_time*100:+.1f}%)")
    
    # 简单的像素级差异分析
    baseline_frames = output_baseline.frames[0]
    lora_frames = output_lora.frames[0]
    
    if len(baseline_frames) == len(lora_frames):
        # 计算前几帧的平均像素差异
        pixel_diffs = []
        for i in range(min(5, len(baseline_frames))):
            baseline_array = np.array(baseline_frames[i].convert('RGB'))
            lora_array = np.array(lora_frames[i].convert('RGB'))
            diff = np.mean(np.abs(baseline_array.astype(float) - lora_array.astype(float)))
            pixel_diffs.append(diff)
        
        avg_pixel_diff = np.mean(pixel_diffs)
        logger.info(f"平均像素差异: {avg_pixel_diff:.2f} (0-255范围)")
        
        if avg_pixel_diff > 1.0:
            logger.info("✅ 检测到显著的像素差异，LoRA权重确实在起作用！")
        else:
            logger.warning("⚠️ 像素差异较小，LoRA效果可能不明显")
    
    # 生成验证报告
    report = {
        "verification_time": datetime.now().isoformat(),
        "lora_checkpoint": str(lora_checkpoint),
        "test_prompt": test_prompt,
        "baseline": {
            "inference_time_seconds": baseline_time,
            "file_size_bytes": baseline_size,
            "video_path": str(baseline_path)
        },
        "with_lora": {
            "inference_time_seconds": lora_time,
            "file_size_bytes": lora_size,
            "video_path": str(lora_path)
        },
        "differences": {
            "time_diff_seconds": time_diff,
            "time_diff_percent": time_diff/baseline_time*100,
            "size_diff_bytes": size_diff,
            "size_diff_percent": size_diff/baseline_size*100,
            "avg_pixel_diff": avg_pixel_diff if 'avg_pixel_diff' in locals() else None
        },
        "conclusion": "LoRA权重确实在起作用" if avg_pixel_diff > 1.0 else "LoRA效果不明显"
    }
    
    report_path = output_dir / "verification_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ 验证报告已保存: {report_path}")
    
    # 结论
    logger.info("=== 验证结论 ===")
    if avg_pixel_diff > 1.0:
        logger.info("🎉 验证成功！LoRA权重确实被正确应用，并产生了可观察的效果。")
        logger.info("💡 如果视频质量不理想，问题可能在于训练数据、训练步数或其他超参数。")
    else:
        logger.info("⚠️ LoRA效果不明显，可能需要检查训练配置或增加训练步数。")
    
    logger.info("📁 请查看生成的视频文件进行主观质量评估。")

def main():
    """主函数"""
    try:
        verify_lora_effectiveness()
    except Exception as e:
        logger.error(f"验证过程中出现错误: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
LoRA训练效果评估脚本 (修复版)

基于深度分析LTX-Video-Trainer项目，修复LoRA权重应用问题。

关键修复：
1. 正确的LoRA适配器初始化流程
2. 使用适配器模式而非融合模式
3. 权重清理和重置机制
4. 与训练时完全一致的配置

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import cv2
import json
import argparse
import torch
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional
import logging
from tqdm import tqdm
import re
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lora_evaluation_fixed.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 添加LTX-Video-Trainer到Python路径
trainer_src_path = Path(__file__).parent / "LTX-Video-Trainer" / "src"
if trainer_src_path.exists():
    sys.path.insert(0, str(trainer_src_path))
    logger.info(f"添加LTX-Video-Trainer路径: {trainer_src_path}")
else:
    logger.warning(f"LTX-Video-Trainer源码路径不存在: {trainer_src_path}")

# 导入必要的模块
try:
    from ltxv_trainer.ltxv_pipeline import LTXConditionPipeline, LTXVideoCondition
    from ltxv_trainer.model_loader import load_ltxv_components
    from peft import LoraConfig
    import PIL.Image
    import imageio
    logger.info("✅ 成功导入所有必要模块")
except ImportError as e:
    logger.error(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class FixedLoRAEvaluator:
    """修复版LoRA评估器"""
    
    def __init__(self, 
                 checkpoints_dir: str,
                 base_model_path: str,
                 test_videos_dir: str,
                 prompt_file: str,
                 output_dir: str,
                 device: str = "cuda"):
        
        self.checkpoints_dir = Path(checkpoints_dir)
        self.base_model_path = Path(base_model_path)
        self.test_videos_dir = Path(test_videos_dir)
        self.prompt_file = Path(prompt_file)
        self.output_dir = Path(output_dir)
        self.device = device
        
        # 初始化变量
        self.pipeline = None
        self.checkpoints = []
        self.test_videos = []
        self.prompts = {}
        
        # LoRA配置（与训练时保持一致）
        self.lora_config = {
            'rank': 32,
            'alpha': 32,
            'dropout': 0.0,
            'target_modules': ["to_k", "to_q", "to_v", "to_out.0"]
        }
        
        logger.info(f"初始化修复版LoRA评估器")
        logger.info(f"设备: {device}")
        logger.info(f"LoRA配置: {self.lora_config}")

    def setup(self) -> bool:
        """设置评估环境"""
        logger.info("开始初始化修复版评估环境...")
        
        # 1. 检查路径
        if not self._check_paths():
            return False
        
        # 2. 扫描checkpoints
        if not self._scan_checkpoints():
            return False
        
        # 3. 扫描测试视频
        if not self._scan_test_videos():
            return False
        
        # 4. 加载提示词
        if not self._load_prompts():
            return False
        
        # 5. 初始化基础管道
        if not self._init_pipeline():
            return False
        
        logger.info("✅ 修复版评估环境初始化完成")
        return True

    def _init_pipeline(self) -> bool:
        """初始化LTXConditionPipeline"""
        try:
            logger.info("初始化基础管道...")
            
            # 设置离线模式
            os.environ["HF_HUB_OFFLINE"] = "1"
            
            # 加载模型组件（与训练时完全一致）
            components = load_ltxv_components(
                model_source=str(self.base_model_path),
                load_text_encoder_in_8bit=False,  # 避免精度问题
                transformer_dtype=torch.bfloat16,  # LoRA训练精度
                vae_dtype=torch.bfloat16,
            )
            
            # 创建管道
            self.pipeline = LTXConditionPipeline(
                vae=components.vae,
                text_encoder=components.text_encoder,
                tokenizer=components.tokenizer,
                transformer=components.transformer,
                scheduler=components.scheduler,
            )
            
            # 移动到设备
            self.pipeline = self.pipeline.to(self.device)
            
            # 启用内存优化
            if hasattr(self.pipeline, 'enable_model_cpu_offload'):
                self.pipeline.enable_model_cpu_offload()
            if hasattr(self.pipeline, 'enable_vae_tiling'):
                self.pipeline.enable_vae_tiling()
            
            logger.info("✅ 基础管道初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化管道失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def _setup_lora_adapter(self) -> bool:
        """设置LoRA适配器（模拟训练时的设置）"""
        try:
            logger.info("设置LoRA适配器...")
            
            # 创建LoRA配置
            lora_config = LoraConfig(
                r=self.lora_config['rank'],
                lora_alpha=self.lora_config['alpha'],
                target_modules=self.lora_config['target_modules'],
                lora_dropout=self.lora_config['dropout'],
                init_lora_weights=True,
            )
            
            # 添加适配器到transformer
            self.pipeline.transformer.add_adapter(lora_config)
            logger.info("✅ LoRA适配器设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置LoRA适配器失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def load_lora_weights_fixed(self, lora_path: Path) -> bool:
        """修复版LoRA权重加载"""
        try:
            logger.info(f"加载LoRA权重: {lora_path.name}")
            
            # 1. 重置transformer到基础状态
            if hasattr(self.pipeline.transformer, 'delete_adapters'):
                self.pipeline.transformer.delete_adapters()
                logger.info("✅ 清理之前的适配器")
            
            # 2. 重新设置LoRA适配器
            if not self._setup_lora_adapter():
                return False
            
            # 3. 加载LoRA权重（使用官方方法）
            from safetensors.torch import load_file
            state_dict = load_file(lora_path)
            
            # 4. 调整权重键名以匹配PEFT格式
            adjusted_state_dict = {}
            for key, value in state_dict.items():
                # 移除可能的前缀
                key = key.replace("transformer.", "")
                # 添加默认适配器名称
                if "lora_A" in key and ".default" not in key:
                    key = key.replace("lora_A", "lora_A.default")
                elif "lora_B" in key and ".default" not in key:
                    key = key.replace("lora_B", "lora_B.default")
                adjusted_state_dict[key] = value
            
            # 5. 加载权重到transformer
            missing_keys, unexpected_keys = self.pipeline.transformer.load_state_dict(
                adjusted_state_dict, strict=False
            )
            
            if unexpected_keys:
                logger.warning(f"未预期的权重键: {unexpected_keys[:5]}...")
            
            # 6. 启用适配器
            self.pipeline.transformer.enable_adapters()
            
            # 7. 设置适配器权重比例
            if hasattr(self.pipeline.transformer, 'set_adapter'):
                self.pipeline.transformer.set_adapter("default")
            
            logger.info(f"✅ LoRA权重加载成功: {lora_path.name}")
            return True
            
        except Exception as e:
            logger.error(f"加载LoRA权重失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def unload_lora_weights_fixed(self) -> bool:
        """修复版LoRA权重卸载"""
        try:
            if hasattr(self.pipeline.transformer, 'disable_adapters'):
                self.pipeline.transformer.disable_adapters()
                logger.info("✅ LoRA适配器已禁用")
            return True
        except Exception as e:
            logger.error(f"卸载LoRA权重失败: {e}")
            return False

    def _check_paths(self) -> bool:
        """检查必要路径"""
        paths = [
            (self.checkpoints_dir, "Checkpoints目录"),
            (self.base_model_path, "基础模型路径"),
            (self.test_videos_dir, "测试视频目录"),
            (self.prompt_file, "提示词文件")
        ]
        
        for path, name in paths:
            if not path.exists():
                logger.error(f"{name}不存在: {path}")
                return False
        
        self.output_dir.mkdir(parents=True, exist_ok=True)
        logger.info("✅ 路径检查通过")
        return True

    def _scan_checkpoints(self) -> bool:
        """扫描checkpoint文件"""
        checkpoint_files = list(self.checkpoints_dir.glob("lora_weights_step_*.safetensors"))
        
        if not checkpoint_files:
            logger.error("未找到checkpoint文件")
            return False
        
        # 按步数排序
        checkpoint_files.sort(key=lambda x: int(re.search(r'step_(\d+)', x.name).group(1)))
        
        self.checkpoints = [
            {
                'name': f.stem,
                'path': f,
                'step': int(re.search(r'step_(\d+)', f.name).group(1))
            }
            for f in checkpoint_files
        ]
        
        logger.info(f"✅ 找到 {len(self.checkpoints)} 个checkpoints")
        return True

    def _scan_test_videos(self) -> bool:
        """扫描测试视频"""
        video_files = list(self.test_videos_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("未找到测试视频")
            return False
        
        self.test_videos = [
            {
                'name': f.stem,
                'path': f,
                'filename': f.name
            }
            for f in video_files
        ]
        
        logger.info(f"✅ 找到 {len(self.test_videos)} 个测试视频")
        return True

    def _load_prompts(self) -> bool:
        """加载提示词"""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.strip().split('\n')
            for i, line in enumerate(lines):
                if '|' in line and i >= 2:  # 跳过表头
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 4:
                        prediction_content = parts[2]
                        motion_description = parts[3]
                        
                        # 从预测内容中提取文件名
                        video_files = [
                            '00819_urbanvideo_test',
                            '00840_urbanvideo_test', 
                            '00846_urbanvideo_test',
                            '01035_NAT2021_test_N02029_4',
                            '01374_NAT2021_test_N08024_3'
                        ]
                        
                        for video_file in video_files:
                            if video_file in prediction_content:
                                self.prompts[video_file] = motion_description
                                break
            
            logger.info(f"✅ 成功加载 {len(self.prompts)} 个提示词")
            return len(self.prompts) > 0
            
        except Exception as e:
            logger.error(f"加载提示词失败: {e}")
            return False

    def generate_video_fixed(self, video_info: Dict, prompt: str, output_path: Path) -> bool:
        """修复版视频生成"""
        try:
            logger.info(f"开始生成视频: {video_info['name']}")

            # 1. 加载条件图像（第一帧）
            cap = cv2.VideoCapture(str(video_info['path']))
            ret, frame = cap.read()
            cap.release()

            if not ret:
                logger.error(f"无法读取视频第一帧: {video_info['path']}")
                return False

            # 转换为PIL图像
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            condition_image = PIL.Image.fromarray(frame_rgb)

            # 2. 创建视频条件
            video_condition = LTXVideoCondition(
                image=condition_image,
                frame_index=0,
            )

            # 3. 设置推理参数（与训练时完全一致）
            width, height = 512, 320  # 训练时的分辨率
            num_frames = 121  # 训练时的帧数
            fps = 25  # 训练时的FPS

            # 4. 设置随机种子（确保可重现）
            generator = torch.Generator(device=self.device).manual_seed(42)

            # 5. 运行推理（使用训练时的参数）
            logger.info(f"开始推理，提示词: {prompt[:50]}...")
            output = self.pipeline(
                prompt=prompt,
                conditions=[video_condition],
                width=width,
                height=height,
                num_frames=num_frames,
                generator=generator,
                guidance_scale=3.0,  # 训练时的guidance_scale
                num_inference_steps=40,  # 训练时的steps
            )

            # 6. 保存视频
            frames = output.frames[0]
            logger.info(f"保存视频，共{len(frames)}帧")

            writer = imageio.get_writer(str(output_path), fps=fps)
            for i, frame in enumerate(frames):
                if hasattr(frame, 'convert'):
                    frame_array = np.array(frame.convert('RGB'))
                else:
                    frame_array = frame
                writer.append_data(frame_array)
            writer.close()

            logger.info(f"✅ 视频生成成功: {output_path}")
            return True

        except Exception as e:
            logger.error(f"生成视频失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def evaluate_single_checkpoint_fixed(self, checkpoint: Dict, video_info: Dict) -> bool:
        """修复版单个checkpoint评估"""
        try:
            logger.info(f"评估checkpoint: {checkpoint['name']} (步数: {checkpoint['step']})")

            # 1. 加载LoRA权重
            if not self.load_lora_weights_fixed(checkpoint['path']):
                return False

            # 2. 获取对应的提示词
            prompt = self.prompts.get(video_info['name'])
            if not prompt:
                logger.warning(f"未找到视频 {video_info['name']} 的提示词")
                prompt = "A beautiful landscape video"  # 默认提示词

            # 3. 创建输出目录
            output_dir = self.output_dir / checkpoint['name']
            output_dir.mkdir(parents=True, exist_ok=True)

            # 4. 生成视频
            output_path = output_dir / f"{video_info['name']}_generated.mp4"
            success = self.generate_video_fixed(video_info, prompt, output_path)

            # 5. 保存首帧图像
            if success:
                first_frame_path = output_dir / f"{video_info['name']}_first_frame.jpg"
                cap = cv2.VideoCapture(str(video_info['path']))
                ret, frame = cap.read()
                cap.release()
                if ret:
                    cv2.imwrite(str(first_frame_path), frame)

                # 6. 保存元数据
                metadata = {
                    'checkpoint': checkpoint['name'],
                    'step': checkpoint['step'],
                    'video_name': video_info['name'],
                    'prompt': prompt,
                    'generation_time': datetime.now().isoformat(),
                    'parameters': {
                        'width': 512,
                        'height': 320,
                        'num_frames': 121,
                        'fps': 25,
                        'guidance_scale': 3.0,
                        'num_inference_steps': 40,
                        'seed': 42
                    },
                    'lora_config': self.lora_config
                }

                metadata_path = output_dir / f"{video_info['name']}_metadata.json"
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False)

            # 7. 卸载LoRA权重
            self.unload_lora_weights_fixed()

            return success

        except Exception as e:
            logger.error(f"评估checkpoint失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def run_evaluation_fixed(self) -> bool:
        """运行修复版完整评估"""
        logger.info("开始修复版LoRA评估...")

        total_tasks = len(self.checkpoints) * len(self.test_videos)
        completed_tasks = 0
        failed_tasks = 0

        # 创建进度条
        with tqdm(total=total_tasks, desc="评估进度") as pbar:
            for checkpoint in self.checkpoints:
                for video_info in self.test_videos:
                    try:
                        success = self.evaluate_single_checkpoint_fixed(checkpoint, video_info)
                        if success:
                            completed_tasks += 1
                        else:
                            failed_tasks += 1

                        pbar.set_postfix({
                            'completed': completed_tasks,
                            'failed': failed_tasks
                        })
                        pbar.update(1)

                    except Exception as e:
                        logger.error(f"评估任务失败: {e}")
                        failed_tasks += 1
                        pbar.update(1)

        # 生成评估报告
        self._generate_evaluation_report_fixed(completed_tasks, failed_tasks)

        logger.info(f"✅ 修复版评估完成！成功: {completed_tasks}, 失败: {failed_tasks}")
        return failed_tasks == 0

    def _generate_evaluation_report_fixed(self, completed: int, failed: int):
        """生成修复版评估报告"""
        report_path = self.output_dir / "evaluation_report_fixed.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# LoRA训练效果评估报告 (修复版)\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**评估统计**:\n")
            f.write(f"- 成功任务: {completed}\n")
            f.write(f"- 失败任务: {failed}\n")
            f.write(f"- 总任务数: {completed + failed}\n")
            f.write(f"- 成功率: {completed/(completed+failed)*100:.1f}%\n\n")

            f.write("**修复内容**:\n")
            f.write("1. ✅ 正确的LoRA适配器初始化流程\n")
            f.write("2. ✅ 使用PEFT适配器模式而非融合模式\n")
            f.write("3. ✅ 权重清理和重置机制\n")
            f.write("4. ✅ 与训练时完全一致的配置参数\n")
            f.write("5. ✅ 修复数据类型和设备位置问题\n\n")

            f.write("**Checkpoints评估结果**:\n")
            for checkpoint in self.checkpoints:
                f.write(f"- {checkpoint['name']} (步数: {checkpoint['step']})\n")

            f.write("\n**测试视频**:\n")
            for video in self.test_videos:
                f.write(f"- {video['name']}\n")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="修复版LoRA训练效果评估脚本")
    parser.add_argument("--checkpoints-dir", default="/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints")
    parser.add_argument("--base-model-path", default="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video")
    parser.add_argument("--test-videos-dir", default="/data1/wzy/LTXV_ALL/demo_video/airscape")
    parser.add_argument("--prompt-file", default="/data1/wzy/LTXV_ALL/demo_video/prompt.txt")
    parser.add_argument("--output-dir", default="/data1/wzy/LTXV_ALL/lora_evaluation_fixed")
    parser.add_argument("--device", default="cuda")
    parser.add_argument("--dry-run", action="store_true", help="只检查配置，不运行评估")

    args = parser.parse_args()

    # 创建修复版评估器
    evaluator = FixedLoRAEvaluator(
        checkpoints_dir=args.checkpoints_dir,
        base_model_path=args.base_model_path,
        test_videos_dir=args.test_videos_dir,
        prompt_file=args.prompt_file,
        output_dir=args.output_dir,
        device=args.device
    )

    # 初始化
    if not evaluator.setup():
        logger.error("初始化失败")
        return

    if args.dry_run:
        logger.info("✅ 配置检查完成，使用 --dry-run 模式")
        return

    # 运行评估
    success = evaluator.run_evaluation_fixed()
    if success:
        logger.info("🎉 修复版评估成功完成！")
    else:
        logger.error("💥 修复版评估过程中出现错误")

if __name__ == "__main__":
    main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试LTX-Video推理功能的脚本
用于验证单GPU和多GPU推理是否正常工作

使用方法:
python test_ltxv_inference.py
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('test_ltxv_inference.log')
    ]
)
logger = logging.getLogger('LTXV-Test')


def check_gpu_availability():
    """检查GPU可用性"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, check=True)
        logger.info("GPU检查通过")
        return True
    except Exception as e:
        logger.error(f"GPU检查失败: {e}")
        return False


def check_required_files():
    """检查必要文件是否存在"""
    required_files = [
        'ltxv_inference.py',
        'ltxv_multi_gpu_inference.py',
        'test_val/test_0730.csv',
        'ltxv_2b_fixed_config.yaml',
        'LTXV_models/LTX-Video/ltxv-2b-0.9.6-dev-04-25.safetensors'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.error("以下必要文件缺失:")
        for file_path in missing_files:
            logger.error(f"  - {file_path}")
        return False
    
    logger.info("所有必要文件检查通过")
    return True


def test_single_gpu_inference():
    """测试单GPU推理"""
    logger.info("开始测试单GPU推理...")
    
    # 选择一个测试视频
    test_video_dir = 'test_val'
    video_files = [f for f in os.listdir(test_video_dir) if f.endswith('.mp4')]
    
    if not video_files:
        logger.error("测试目录中没有找到视频文件")
        return False
    
    test_video = video_files[0]  # 使用第一个视频进行测试
    output_dir = f"test_output_single_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    cmd = [
        sys.executable, 'ltxv_inference.py',
        '--video_dir', test_video_dir,
        '--prompts_csv', 'test_val/test_0730.csv',
        '--output_dir', output_dir,
        '--single_video', test_video,
        '--num_frames', '49',  # 使用较少帧数进行快速测试
        '--height', '384',
        '--width', '640'
    ]
    
    try:
        logger.info(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info("单GPU推理测试成功")
            
            # 检查输出文件
            if os.path.exists(output_dir):
                output_files = [f for f in os.listdir(output_dir) if f.endswith('.mp4')]
                if output_files:
                    logger.info(f"生成了 {len(output_files)} 个输出文件")
                    return True
                else:
                    logger.warning("没有找到输出视频文件")
                    return False
            else:
                logger.warning("输出目录不存在")
                return False
        else:
            logger.error("单GPU推理测试失败")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("单GPU推理测试超时")
        return False
    except Exception as e:
        logger.error(f"单GPU推理测试出错: {e}")
        return False


def test_multi_gpu_inference():
    """测试多GPU推理"""
    logger.info("开始测试多GPU推理...")
    
    output_dir = f"test_output_multi_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    cmd = [
        sys.executable, 'ltxv_multi_gpu_inference.py',
        '--video_dir', 'test_val',
        '--prompts_csv', 'test_val/test_0730.csv',
        '--output_dir', output_dir,
        '--num_frames', '49',  # 使用较少帧数进行快速测试
        '--height', '384',
        '--width', '640',
        '--min_free_memory', '8000',  # 降低内存要求
        '--max_usage_percent', '30'
    ]
    
    try:
        logger.info(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            logger.info("多GPU推理测试成功")
            
            # 检查输出文件
            if os.path.exists(output_dir):
                total_files = 0
                for item in os.listdir(output_dir):
                    item_path = os.path.join(output_dir, item)
                    if os.path.isdir(item_path):
                        gpu_files = [f for f in os.listdir(item_path) if f.endswith('.mp4')]
                        total_files += len(gpu_files)
                        logger.info(f"{item}: 生成了 {len(gpu_files)} 个文件")
                
                if total_files > 0:
                    logger.info(f"总共生成了 {total_files} 个输出文件")
                    return True
                else:
                    logger.warning("没有找到输出视频文件")
                    return False
            else:
                logger.warning("输出目录不存在")
                return False
        else:
            logger.error("多GPU推理测试失败")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("多GPU推理测试超时")
        return False
    except Exception as e:
        logger.error(f"多GPU推理测试出错: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始LTX-Video推理功能测试")
    
    # 检查GPU可用性
    if not check_gpu_availability():
        logger.error("GPU不可用，无法进行测试")
        return
    
    # 检查必要文件
    if not check_required_files():
        logger.error("必要文件缺失，无法进行测试")
        return
    
    # 测试单GPU推理
    single_gpu_success = test_single_gpu_inference()
    
    # 测试多GPU推理
    multi_gpu_success = test_multi_gpu_inference()
    
    # 总结测试结果
    logger.info("=" * 60)
    logger.info("测试结果总结:")
    logger.info(f"单GPU推理: {'✓ 成功' if single_gpu_success else '✗ 失败'}")
    logger.info(f"多GPU推理: {'✓ 成功' if multi_gpu_success else '✗ 失败'}")
    
    if single_gpu_success and multi_gpu_success:
        logger.info("所有测试通过！")
    else:
        logger.warning("部分测试失败，请检查日志")


if __name__ == "__main__":
    main()

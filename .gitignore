# 视频文件
*.mp4
*.avi
*.mov
*.mkv
*.wmv
*.flv
*.webm
*.m4v

# 模型文件
*.pth
*.pt
*.bin
*.safetensors
*.ckpt
*.pkl
*.h5
*.pb
*.onnx
*.tflite
*.mlmodel
*.model
*.weights

# 大型数据文件
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz
*.zip
*.rar
*.7z
*.gz
*.bz2
*.xz

# 模型目录
models/
LTXV_models/
**/models/
weights/
checkpoints/
pretrained/

# 数据集和视频目录
dataset/
datasets/
data/
videos/
**/videos/
demo_video/Best_video/
**/Best_video/

# 日志文件
*.log
logs/
**/logs/

# 临时文件
temp/
tmp/
**/temp/
**/tmp/

# Python缓存
__pycache__/
*.py[cod]
*~
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# IDE设置
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 其他大文件 (超过100MB的文件通常包括)
*.deb
*.rpm
*.dmg
*.iso
*.img

# HuggingFace缓存
.cache/
**/cache/

# 特定的大文件扩展名
*.npz
*.npy
spiece.model
*.tfrecord

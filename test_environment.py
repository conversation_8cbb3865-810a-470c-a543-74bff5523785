#!/usr/bin/env python3
"""
环境测试脚本

用于诊断LoRA评估脚本的环境问题
"""

import os
import sys
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_basic_imports():
    """测试基础导入"""
    logger.info("=== 测试基础导入 ===")
    
    try:
        import torch
        logger.info(f"✅ PyTorch版本: {torch.__version__}")
        logger.info(f"✅ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            logger.info(f"✅ CUDA版本: {torch.version.cuda}")
            logger.info(f"✅ GPU数量: {torch.cuda.device_count()}")
    except ImportError as e:
        logger.error(f"❌ PyTorch导入失败: {e}")
    
    try:
        import cv2
        logger.info(f"✅ OpenCV版本: {cv2.__version__}")
    except ImportError as e:
        logger.error(f"❌ OpenCV导入失败: {e}")
    
    try:
        import PIL.Image
        logger.info("✅ PIL导入成功")
    except ImportError as e:
        logger.error(f"❌ PIL导入失败: {e}")
    
    try:
        import imageio
        logger.info(f"✅ imageio版本: {imageio.__version__}")
    except ImportError as e:
        logger.error(f"❌ imageio导入失败: {e}")
    
    try:
        import tqdm
        logger.info("✅ tqdm导入成功")
    except ImportError as e:
        logger.error(f"❌ tqdm导入失败: {e}")

def test_paths():
    """测试路径"""
    logger.info("=== 测试路径 ===")
    
    paths_to_check = [
        "/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints",
        "/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video",
        "/data1/wzy/LTXV_ALL/demo_video/airscape",
        "/data1/wzy/LTXV_ALL/demo_video/prompt.txt",
        "/data1/wzy/LTXV_ALL/LTX-Video-Trainer",
        "/data1/wzy/LTXV_ALL/LTX-Video-Trainer/src"
    ]
    
    for path_str in paths_to_check:
        path = Path(path_str)
        if path.exists():
            if path.is_file():
                logger.info(f"✅ 文件存在: {path}")
            else:
                file_count = len(list(path.glob("*"))) if path.is_dir() else 0
                logger.info(f"✅ 目录存在: {path} (包含{file_count}个文件/目录)")
        else:
            logger.error(f"❌ 路径不存在: {path}")

def test_ltxv_trainer_import():
    """测试LTX-Video-Trainer导入"""
    logger.info("=== 测试LTX-Video-Trainer导入 ===")
    
    # 添加路径
    trainer_src_path = Path("/data1/wzy/LTXV_ALL/LTX-Video-Trainer/src")
    if trainer_src_path.exists():
        sys.path.insert(0, str(trainer_src_path))
        logger.info(f"✅ 添加路径: {trainer_src_path}")
    else:
        logger.error(f"❌ LTX-Video-Trainer源码路径不存在: {trainer_src_path}")
        return
    
    # 测试导入
    try:
        import ltxv_trainer
        logger.info("✅ ltxv_trainer包导入成功")
    except ImportError as e:
        logger.error(f"❌ ltxv_trainer包导入失败: {e}")
    
    try:
        from ltxv_trainer.ltxv_pipeline import LTXConditionPipeline, LTXVideoCondition
        logger.info("✅ LTXConditionPipeline导入成功")
    except ImportError as e:
        logger.error(f"❌ LTXConditionPipeline导入失败: {e}")
    
    try:
        from ltxv_trainer.model_loader import load_ltxv_components, LtxvModelVersion
        logger.info("✅ model_loader导入成功")
    except ImportError as e:
        logger.error(f"❌ model_loader导入失败: {e}")

def test_diffusers():
    """测试Diffusers相关导入"""
    logger.info("=== 测试Diffusers导入 ===")
    
    try:
        import diffusers
        logger.info(f"✅ Diffusers版本: {diffusers.__version__}")
    except ImportError as e:
        logger.error(f"❌ Diffusers导入失败: {e}")
        return
    
    try:
        from diffusers.loaders import LTXVideoLoraLoaderMixin
        logger.info("✅ LTXVideoLoraLoaderMixin导入成功")
    except ImportError as e:
        logger.error(f"❌ LTXVideoLoraLoaderMixin导入失败: {e}")
    
    try:
        from diffusers.models.transformers import LTXVideoTransformer3DModel
        logger.info("✅ LTXVideoTransformer3DModel导入成功")
    except ImportError as e:
        logger.error(f"❌ LTXVideoTransformer3DModel导入失败: {e}")

def test_checkpoint_files():
    """测试checkpoint文件"""
    logger.info("=== 测试Checkpoint文件 ===")
    
    checkpoints_dir = Path("/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints")
    if not checkpoints_dir.exists():
        logger.error(f"❌ Checkpoints目录不存在: {checkpoints_dir}")
        return
    
    checkpoint_files = list(checkpoints_dir.glob("*.safetensors"))
    logger.info(f"✅ 找到{len(checkpoint_files)}个checkpoint文件:")
    for file in checkpoint_files[:5]:  # 只显示前5个
        logger.info(f"  - {file.name}")
    if len(checkpoint_files) > 5:
        logger.info(f"  ... 还有{len(checkpoint_files) - 5}个文件")

def test_video_files():
    """测试视频文件"""
    logger.info("=== 测试视频文件 ===")
    
    videos_dir = Path("/data1/wzy/LTXV_ALL/demo_video/airscape")
    if not videos_dir.exists():
        logger.error(f"❌ 视频目录不存在: {videos_dir}")
        return
    
    video_files = list(videos_dir.glob("*.mp4"))
    logger.info(f"✅ 找到{len(video_files)}个视频文件:")
    for file in video_files:
        logger.info(f"  - {file.name}")

def main():
    """主函数"""
    logger.info("开始环境诊断...")
    
    test_basic_imports()
    test_paths()
    test_diffusers()
    test_ltxv_trainer_import()
    test_checkpoint_files()
    test_video_files()
    
    logger.info("环境诊断完成")

if __name__ == "__main__":
    main()

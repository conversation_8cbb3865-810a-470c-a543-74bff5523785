#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LTX-Video 交互式推理脚本
提供友好的交互界面，支持图片和视频输入、prompt输入和推理执行

使用方法:
python ltxv_interactive_inference.py
"""

import os
import gc
import torch
import logging
import sys
import random
import numpy as np
import cv2
import time
from pathlib import Path
from datetime import datetime
from typing import Optional, Tuple, Dict, Any

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("警告: PIL/Pillow 未安装，图片支持功能将受限")

# 添加LTX-Video路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'LTX-Video'))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ltxv_interactive.log')
    ]
)
logger = logging.getLogger('LTXV-Interactive')

# 导入LTX-Video相关模块
from ltx_video.inference import infer, InferenceConfig


class Colors:
    """终端颜色定义"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


class InteractiveLTXV:
    """交互式LTX-Video推理类"""
    
    def __init__(self):
        self.video_path = None
        self.video_info = None
        self.file_type = None  # 'image' 或 'video'
        self.prompt = None
        self.output_dir = "outputs_interactive"
        self.config_path = "/data1/wzy/LTXV_ALL/ltxv_2b_fixed_config.yaml"
        self.session_history = []
        
        # 推理参数
        self.inference_params = {
            'height': 448,
            'width': 768,
            'num_frames': 97,
            'seed': 42,
            'save_first_frame': True
        }
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 检查CUDA可用性
        if not torch.cuda.is_available():
            self.print_error("CUDA不可用，无法使用GPU推理")
            sys.exit(1)
        
        self.setup_environment()
    
    def setup_environment(self):
        """设置基本环境"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.backends.cudnn.benchmark = True
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
        logger.info("环境设置完成")
    
    def print_header(self, text: str):
        """打印标题"""
        print(f"\n{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}")
        print(f"{Colors.HEADER}{Colors.BOLD}{text.center(60)}{Colors.ENDC}")
        print(f"{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}\n")
    
    def print_info(self, text: str):
        """打印信息"""
        print(f"{Colors.OKBLUE}ℹ {text}{Colors.ENDC}")
    
    def print_success(self, text: str):
        """打印成功信息"""
        print(f"{Colors.OKGREEN}✓ {text}{Colors.ENDC}")
    
    def print_warning(self, text: str):
        """打印警告"""
        print(f"{Colors.WARNING}⚠ {text}{Colors.ENDC}")
    
    def print_error(self, text: str):
        """打印错误"""
        print(f"{Colors.FAIL}✗ {text}{Colors.ENDC}")
    
    def print_prompt(self, text: str):
        """打印提示"""
        print(f"{Colors.OKCYAN}➤ {text}{Colors.ENDC}", end="")
    
    def get_user_input(self, prompt_text: str, default: str = None) -> str:
        """获取用户输入"""
        if default:
            self.print_prompt(f"{prompt_text} [{default}]: ")
        else:
            self.print_prompt(f"{prompt_text}: ")
        
        try:
            user_input = input().strip()
            if not user_input and default:
                return default
            return user_input
        except KeyboardInterrupt:
            print(f"\n{Colors.WARNING}操作已取消{Colors.ENDC}")
            return None
        except EOFError:
            print(f"\n{Colors.WARNING}输入结束{Colors.ENDC}")
            return None
    
    def get_yes_no_input(self, prompt_text: str, default: bool = True) -> bool:
        """获取是/否输入"""
        default_str = "Y/n" if default else "y/N"
        response = self.get_user_input(f"{prompt_text} ({default_str})")
        
        if response is None:
            return False
        
        if not response:
            return default
        
        return response.lower() in ['y', 'yes', '是', '确定']

    def detect_file_type(self, file_path: str) -> str:
        """检测文件类型（图片或视频）"""
        # 支持的图片格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp']
        # 支持的视频格式
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v', '.3gp']

        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext in image_extensions:
            return 'image'
        elif file_ext in video_extensions:
            return 'video'
        else:
            return 'unknown'

    def extract_image_info(self, image_path: str) -> Optional[Dict[str, Any]]:
        """提取图片信息"""
        try:
            # 使用OpenCV读取图片
            img_cv = cv2.imread(image_path)
            if img_cv is None:
                return None

            height, width = img_cv.shape[:2]
            channels = img_cv.shape[2] if len(img_cv.shape) == 3 else 1

            # 尝试使用PIL获取更多信息
            format_name = "Unknown"
            mode = "Unknown"

            if PIL_AVAILABLE:
                try:
                    with Image.open(image_path) as img:
                        format_name = img.format or "Unknown"
                        mode = img.mode or "Unknown"
                except Exception:
                    pass
            else:
                # 从文件扩展名推断格式
                ext = os.path.splitext(image_path)[1].lower()
                format_map = {'.jpg': 'JPEG', '.jpeg': 'JPEG', '.png': 'PNG',
                             '.bmp': 'BMP', '.tiff': 'TIFF', '.tif': 'TIFF', '.webp': 'WEBP'}
                format_name = format_map.get(ext, "Unknown")
                mode = "RGB" if channels == 3 else "L" if channels == 1 else "Unknown"

            return {
                'width': width,
                'height': height,
                'format': format_name,
                'mode': mode,
                'channels': channels,
                'first_frame': img_cv,  # 对于图片，直接使用原图作为"第一帧"
                'file_size': os.path.getsize(image_path) / (1024 * 1024),  # MB
                'is_image': True
            }
        except Exception as e:
            logger.error(f"提取图片信息时出错: {e}")
            return None

    def extract_video_info(self, video_path: str) -> Optional[Dict[str, Any]]:
        """提取视频信息"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
            
            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0
            
            # 读取第一帧
            ret, first_frame = cap.read()
            cap.release()
            
            if not ret:
                return None
            
            return {
                'width': width,
                'height': height,
                'fps': fps,
                'frame_count': frame_count,
                'duration': duration,
                'first_frame': first_frame,
                'file_size': os.path.getsize(video_path) / (1024 * 1024),  # MB
                'is_image': False
            }
        except Exception as e:
            logger.error(f"提取视频信息时出错: {e}")
            return None
    
    def save_first_frame(self, video_info: Dict[str, Any], output_path: str) -> bool:
        """保存第一帧"""
        try:
            cv2.imwrite(output_path, video_info['first_frame'])
            return True
        except Exception as e:
            logger.error(f"保存第一帧时出错: {e}")
            return False
    
    def display_image_info(self, image_info: Dict[str, Any]):
        """显示图片信息"""
        print(f"\n{Colors.OKGREEN}图片信息：{Colors.ENDC}")
        print(f"  分辨率: {image_info['width']}x{image_info['height']}")
        print(f"  格式: {image_info['format']}")
        print(f"  颜色模式: {image_info['mode']}")
        print(f"  通道数: {image_info['channels']}")
        print(f"  文件大小: {image_info['file_size']:.2f} MB")

    def display_video_info(self, video_info: Dict[str, Any]):
        """显示视频信息"""
        print(f"\n{Colors.OKGREEN}视频信息：{Colors.ENDC}")
        print(f"  分辨率: {video_info['width']}x{video_info['height']}")
        print(f"  帧率: {video_info['fps']:.2f} fps")
        print(f"  总帧数: {video_info['frame_count']}")
        print(f"  时长: {video_info['duration']:.2f} 秒")
        print(f"  文件大小: {video_info['file_size']:.2f} MB")

    def display_media_info(self, media_info: Dict[str, Any]):
        """显示媒体信息（图片或视频）"""
        if media_info.get('is_image', False):
            self.display_image_info(media_info)
        else:
            self.display_video_info(media_info)
    
    def get_gpu_memory_info(self) -> Dict[str, float]:
        """获取GPU内存使用信息"""
        if not torch.cuda.is_available():
            return {"cuda": "不可用"}
        
        memory_info = {
            "allocated": torch.cuda.memory_allocated() / 1024**3,  # GB
            "reserved": torch.cuda.memory_reserved() / 1024**3,    # GB
            "total": torch.cuda.get_device_properties(0).total_memory / 1024**3,  # GB
        }
        memory_info["free"] = memory_info["total"] - memory_info["allocated"]
        return memory_info
    
    def adjust_config_for_memory(self, config: InferenceConfig, available_memory_gb: float):
        """根据可用内存调整配置"""
        if available_memory_gb < 25:
            config.num_frames = min(config.num_frames, 25)
            config.height = min(config.height, 384)
            config.width = min(config.width, 640)
            self.print_warning("内存不足，使用极小配置")
        elif available_memory_gb < 30:
            config.num_frames = min(config.num_frames, 49)
            config.height = min(config.height, 512)
            config.width = min(config.width, 768)
            self.print_warning("内存有限，使用小配置")
        elif available_memory_gb < 35:
            config.num_frames = min(config.num_frames, 81)
            config.height = min(config.height, 576)
            config.width = min(config.width, 1024)
            self.print_info("使用中等配置")
        else:
            self.print_success("内存充足，使用原始配置")
        
        return config

    def input_media_path(self) -> bool:
        """输入和验证媒体文件路径（图片或视频）"""
        self.print_header("步骤 1: 选择输入文件")

        while True:
            self.print_info("请输入图片或视频文件路径（支持相对路径和绝对路径）")
            self.print_info("支持的图片格式: jpg, jpeg, png, bmp, tiff, webp")
            self.print_info("支持的视频格式: mp4, avi, mov, mkv, wmv, flv")
            self.print_info("示例: demo_video/airscape/00819_urbanvideo_test.mp4")
            self.print_info("示例: images/sample.jpg")

            media_path = self.get_user_input("文件路径")
            if media_path is None:
                return False

            if not media_path:
                self.print_error("请输入有效的文件路径")
                continue

            # 检查文件是否存在
            if not os.path.exists(media_path):
                self.print_error(f"文件不存在: {media_path}")
                if not self.get_yes_no_input("是否重新输入"):
                    return False
                continue

            # 检测文件类型
            file_type = self.detect_file_type(media_path)

            if file_type == 'unknown':
                self.print_warning("无法识别的文件格式，是否继续？")
                if not self.get_yes_no_input("继续处理"):
                    continue
                # 默认按视频处理
                file_type = 'video'

            # 显示检测到的文件类型
            if file_type == 'image':
                self.print_success(f"检测到图片文件: {os.path.basename(media_path)}")
            else:
                self.print_success(f"检测到视频文件: {os.path.basename(media_path)}")

            # 根据文件类型提取信息
            self.print_info(f"正在分析{file_type == 'image' and '图片' or '视频'}文件...")

            if file_type == 'image':
                media_info = self.extract_image_info(media_path)
            else:
                media_info = self.extract_video_info(media_path)

            if media_info is None:
                self.print_error(f"无法读取{file_type == 'image' and '图片' or '视频'}文件，请检查文件格式")
                if not self.get_yes_no_input("是否重新输入"):
                    return False
                continue

            # 显示文件信息
            self.display_media_info(media_info)

            # 询问是否保存预览（对于图片是复制，对于视频是第一帧）
            preview_text = "是否保存图片副本作为预览" if file_type == 'image' else "是否保存第一帧作为预览"
            if self.get_yes_no_input(preview_text, True):
                base_name = os.path.splitext(os.path.basename(media_path))[0]
                preview_path = os.path.join(self.output_dir, f"{base_name}_preview.jpg")

                if self.save_first_frame(media_info, preview_path):
                    preview_type = "图片副本" if file_type == 'image' else "第一帧预览"
                    self.print_success(f"{preview_type}已保存: {preview_path}")
                else:
                    self.print_warning("保存预览失败")

            # 确认使用此文件
            confirm_text = f"确认使用此{file_type == 'image' and '图片' or '视频'}文件"
            if self.get_yes_no_input(confirm_text, True):
                self.video_path = media_path  # 保持变量名一致性
                self.video_info = media_info  # 保持变量名一致性
                self.file_type = file_type
                self.session_history.append(f"选择{file_type == 'image' and '图片' or '视频'}: {media_path}")
                return True

    def input_prompt(self) -> bool:
        """输入和处理prompt"""
        self.print_header("步骤 2: 输入文本描述 (Prompt)")

        self.print_info("请输入视频生成的文本描述")
        self.print_info("提示：描述应该包含相机运动、场景内容和动作细节")
        print(f"\n{Colors.OKCYAN}示例格式：{Colors.ENDC}")
        print("  The drone moved forward with its camera pointed straight ahead,")
        print("  capturing a stationary view of high-rise buildings, a landscaped garden, and a pond.")
        print()

        while True:
            self.print_info("输入方式选择:")
            print("  1. 直接输入文本")
            print("  2. 从文件读取")
            print("  3. 使用多行输入模式")

            choice = self.get_user_input("请选择输入方式 (1-3)", "1")
            if choice is None:
                return False

            prompt_text = None

            if choice == "1":
                # 直接输入
                prompt_text = self.get_user_input("请输入prompt")
                if prompt_text is None:
                    return False

            elif choice == "2":
                # 从文件读取
                file_path = self.get_user_input("请输入文件路径")
                if file_path is None:
                    return False

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        prompt_text = f.read().strip()
                    self.print_success(f"从文件读取prompt: {file_path}")
                except Exception as e:
                    self.print_error(f"读取文件失败: {e}")
                    continue

            elif choice == "3":
                # 多行输入模式
                self.print_info("多行输入模式（输入空行结束）:")
                lines = []
                while True:
                    line = input()
                    if not line.strip():
                        break
                    lines.append(line)
                prompt_text = " ".join(lines).strip()

            else:
                self.print_error("无效选择，请重新输入")
                continue

            if not prompt_text:
                self.print_error("Prompt不能为空")
                if not self.get_yes_no_input("是否重新输入"):
                    return False
                continue

            # 显示prompt预览
            print(f"\n{Colors.OKGREEN}Prompt预览：{Colors.ENDC}")
            print(f"  {prompt_text}")
            print(f"\n  长度: {len(prompt_text)} 字符")
            print(f"  单词数: {len(prompt_text.split())} 个")

            # 确认prompt
            if self.get_yes_no_input("确认使用此prompt", True):
                self.prompt = prompt_text
                self.session_history.append(f"输入prompt: {prompt_text[:50]}...")
                return True

    def configure_inference_params(self) -> bool:
        """配置推理参数"""
        self.print_header("步骤 3: 配置推理参数")

        self.print_info("当前推理参数:")
        for key, value in self.inference_params.items():
            print(f"  {key}: {value}")

        if not self.get_yes_no_input("是否修改推理参数", False):
            return True

        # 配置各个参数
        params_config = {
            'height': ('输出视频高度', 'int', 384, 1024),
            'width': ('输出视频宽度', 'int', 640, 1536),
            'num_frames': ('生成帧数', 'int', 25, 200),
            'seed': ('随机种子', 'int', 0, 999999),
        }

        for param, (desc, param_type, min_val, max_val) in params_config.items():
            current_val = self.inference_params[param]
            new_val = self.get_user_input(f"{desc} ({min_val}-{max_val})", str(current_val))

            if new_val is None:
                return False

            try:
                if param_type == 'int':
                    new_val = int(new_val)
                    if min_val <= new_val <= max_val:
                        self.inference_params[param] = new_val
                        self.print_success(f"{desc}已设置为: {new_val}")
                    else:
                        self.print_warning(f"值超出范围，保持原值: {current_val}")
            except ValueError:
                self.print_warning(f"无效输入，保持原值: {current_val}")

        # 配置是否保存第一帧
        save_frame = self.get_yes_no_input("是否保存提取的第一帧",
                                         self.inference_params['save_first_frame'])
        self.inference_params['save_first_frame'] = save_frame

        self.session_history.append("配置推理参数")
        return True

    def execute_inference(self) -> bool:
        """执行推理"""
        self.print_header("步骤 4: 执行推理")

        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(self.video_path))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_subdir = os.path.join(self.output_dir, f"{base_name}_{timestamp}")

        self.print_info(f"输出目录: {output_subdir}")

        # 显示推理配置摘要
        print(f"\n{Colors.OKGREEN}推理配置摘要：{Colors.ENDC}")
        input_type = "图片" if self.file_type == 'image' else "视频"
        print(f"  输入{input_type}: {self.video_path}")
        print(f"  输出目录: {output_subdir}")
        print(f"  分辨率: {self.inference_params['height']}x{self.inference_params['width']}")
        print(f"  帧数: {self.inference_params['num_frames']}")
        print(f"  随机种子: {self.inference_params['seed']}")
        print(f"  配置文件: {self.config_path}")

        if not self.get_yes_no_input("确认开始推理", True):
            return False

        try:
            # 检查配置文件
            if not os.path.exists(self.config_path):
                self.print_error(f"配置文件不存在: {self.config_path}")
                # 尝试使用备用配置
                backup_configs = [
                    "ltxv_2b_fixed_config.yaml",
                    "ltxv_ultra_optimized_config.yaml",
                    "ltxv_2b_fixed_config_rz.yaml"
                ]

                for backup in backup_configs:
                    if os.path.exists(backup):
                        self.config_path = backup
                        self.print_success(f"使用备用配置: {backup}")
                        break
                else:
                    self.print_error("未找到可用的配置文件")
                    return False

            # 显示GPU内存状态
            memory_info = self.get_gpu_memory_info()
            self.print_info(f"GPU内存状态: {memory_info['free']:.1f}GB 可用 / {memory_info['total']:.1f}GB 总计")

            # 创建推理配置
            config = InferenceConfig(
                prompt=self.prompt,
                output_path=output_subdir,
                pipeline_config=self.config_path,
                height=self.inference_params['height'],
                width=self.inference_params['width'],
                num_frames=self.inference_params['num_frames'],
                conditioning_media_paths=[self.video_path],
                conditioning_start_frames=[0],
                seed=self.inference_params['seed'],
                offload_to_cpu=False
            )

            # 根据内存情况调整配置
            config = self.adjust_config_for_memory(config, memory_info['free'])

            # 保存第一帧或图片副本（如果需要）
            if self.inference_params['save_first_frame']:
                if self.file_type == 'image':
                    first_frame_path = os.path.join(self.output_dir, f"{base_name}_{timestamp}_source_image.jpg")
                    save_text = "源图片副本"
                else:
                    first_frame_path = os.path.join(self.output_dir, f"{base_name}_{timestamp}_first_frame.jpg")
                    save_text = "第一帧"

                if self.save_first_frame(self.video_info, first_frame_path):
                    self.print_success(f"{save_text}已保存: {first_frame_path}")

            self.print_info("开始推理...")
            self.print_warning("推理过程可能需要几分钟，请耐心等待...")

            start_time = time.time()

            # 执行推理
            infer(config)

            end_time = time.time()
            duration = end_time - start_time

            self.print_success(f"推理完成！耗时: {duration:.1f} 秒")
            self.print_success(f"输出保存到: {output_subdir}")

            # 显示输出文件信息
            if os.path.exists(output_subdir):
                output_files = [f for f in os.listdir(output_subdir) if f.endswith('.mp4')]
                if output_files:
                    output_file = os.path.join(output_subdir, output_files[0])
                    file_size = os.path.getsize(output_file) / (1024 * 1024)
                    self.print_info(f"生成视频: {output_files[0]} ({file_size:.2f} MB)")

            # 显示推理后的GPU内存状态
            post_memory = self.get_gpu_memory_info()
            self.print_info(f"推理后GPU内存: {post_memory['free']:.1f}GB 可用")

            # 清理内存
            gc.collect()
            torch.cuda.empty_cache()

            self.session_history.append(f"推理完成: {output_subdir}")
            return True

        except torch.cuda.OutOfMemoryError as e:
            self.print_error("CUDA内存不足！")
            self.print_info("建议解决方案:")
            print("  1. 降低输出分辨率")
            print("  2. 减少生成帧数")
            print("  3. 重启程序清理内存")
            logger.error(f"CUDA OOM: {e}")
            return False

        except Exception as e:
            self.print_error(f"推理过程中出错: {e}")
            logger.error(f"推理错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def show_session_summary(self):
        """显示会话摘要"""
        self.print_header("会话摘要")

        if self.session_history:
            self.print_info("本次会话操作历史:")
            for i, action in enumerate(self.session_history, 1):
                print(f"  {i}. {action}")
        else:
            self.print_info("本次会话无操作记录")

        # 显示输出目录内容
        if os.path.exists(self.output_dir):
            files = os.listdir(self.output_dir)
            if files:
                self.print_info(f"输出目录 ({self.output_dir}) 包含:")
                for file in sorted(files):
                    file_path = os.path.join(self.output_dir, file)
                    if os.path.isdir(file_path):
                        print(f"  📁 {file}/")
                    else:
                        print(f"  📄 {file}")

    def run(self):
        """运行交互式推理"""
        self.print_header("LTX-Video 交互式推理工具")

        self.print_info("欢迎使用LTX-Video交互式推理工具！")
        self.print_info("支持图片和视频文件作为输入")
        self.print_info(f"GPU设备: {torch.cuda.get_device_name()}")

        while True:
            try:
                # 步骤1: 输入媒体文件路径
                if not self.input_media_path():
                    break

                # 步骤2: 输入prompt
                if not self.input_prompt():
                    if self.get_yes_no_input("是否重新开始"):
                        continue
                    break

                # 步骤3: 配置推理参数
                if not self.configure_inference_params():
                    if self.get_yes_no_input("是否重新开始"):
                        continue
                    break

                # 步骤4: 执行推理
                if not self.execute_inference():
                    if self.get_yes_no_input("是否重新开始"):
                        continue
                    break

                # 询问是否继续
                file_type_text = "图片或视频"
                if not self.get_yes_no_input(f"是否处理另一个{file_type_text}", False):
                    break

                # 重置状态
                self.video_path = None
                self.video_info = None
                self.file_type = None
                self.prompt = None

            except KeyboardInterrupt:
                print(f"\n{Colors.WARNING}程序被用户中断{Colors.ENDC}")
                break
            except Exception as e:
                self.print_error(f"程序出现异常: {e}")
                logger.error(f"程序异常: {e}")
                if not self.get_yes_no_input("是否继续"):
                    break

        # 显示会话摘要
        self.show_session_summary()
        self.print_success("感谢使用LTX-Video交互式推理工具！")


def main():
    """主函数"""
    try:
        app = InteractiveLTXV()
        app.run()
    except Exception as e:
        print(f"{Colors.FAIL}程序启动失败: {e}{Colors.ENDC}")
        sys.exit(1)


if __name__ == "__main__":
    main()

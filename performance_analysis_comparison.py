#!/usr/bin/env python3
"""
LTX-Video推理脚本性能对比分析

深入分析两个推理脚本的性能差异：
1. evaluate_lora_improved.py (带LoRA，使用LTX-Video-Trainer)
2. ltxv_interactive_inference.py (无LoRA，使用LTX-Video)

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import time
import torch
import psutil
import logging
import json
import cv2
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List
import subprocess
import threading

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.metrics = {
            'gpu_memory': [],
            'gpu_utilization': [],
            'cpu_usage': [],
            'timestamps': []
        }
        self.monitor_thread = None
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.metrics = {
            'gpu_memory': [],
            'gpu_utilization': [],
            'cpu_usage': [],
            'timestamps': []
        }
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # GPU内存使用
                if torch.cuda.is_available():
                    gpu_memory = torch.cuda.memory_allocated() / 1024**3  # GB
                    gpu_memory_max = torch.cuda.max_memory_allocated() / 1024**3  # GB
                else:
                    gpu_memory = 0
                    gpu_memory_max = 0
                
                # CPU使用率
                cpu_usage = psutil.cpu_percent()
                
                # 记录数据
                self.metrics['gpu_memory'].append(gpu_memory)
                self.metrics['cpu_usage'].append(cpu_usage)
                self.metrics['timestamps'].append(time.time())
                
                time.sleep(1)  # 每秒采样一次
                
            except Exception as e:
                logger.error(f"监控错误: {e}")
                break
    
    def get_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        if not self.metrics['timestamps']:
            return {}
        
        return {
            'duration_seconds': self.metrics['timestamps'][-1] - self.metrics['timestamps'][0],
            'gpu_memory_peak_gb': max(self.metrics['gpu_memory']) if self.metrics['gpu_memory'] else 0,
            'gpu_memory_avg_gb': np.mean(self.metrics['gpu_memory']) if self.metrics['gpu_memory'] else 0,
            'cpu_usage_avg_percent': np.mean(self.metrics['cpu_usage']) if self.metrics['cpu_usage'] else 0,
            'cpu_usage_peak_percent': max(self.metrics['cpu_usage']) if self.metrics['cpu_usage'] else 0,
        }

class InferenceComparator:
    """推理性能对比器"""
    
    def __init__(self):
        self.test_video = "/data1/wzy/LTXV_ALL/demo_video/airscape/00819_urbanvideo_test.mp4"
        self.test_prompt = "A drone shot of a city street with cars and buildings, urban landscape, aerial view"
        self.output_dir = Path("/data1/wzy/LTXV_ALL/performance_comparison")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 测试参数（保持一致）
        self.test_params = {
            'width': 512,
            'height': 320,
            'num_frames': 121,
            'fps': 25,
            'guidance_scale': 3.0,
            'num_inference_steps': 40,
            'seed': 42
        }
        
        logger.info("初始化推理性能对比器")
        logger.info(f"测试视频: {self.test_video}")
        logger.info(f"测试参数: {self.test_params}")
    
    def test_ltxv_trainer_method(self) -> Dict[str, Any]:
        """测试LTX-Video-Trainer方法（带LoRA）"""
        logger.info("=== 测试LTX-Video-Trainer方法 ===")
        
        # 添加路径
        trainer_src_path = Path("/data1/wzy/LTXV_ALL/LTX-Video-Trainer/src")
        if trainer_src_path.exists():
            sys.path.insert(0, str(trainer_src_path))
        
        try:
            from ltxv_trainer.ltxv_pipeline import LTXConditionPipeline, LTXVideoCondition
            from ltxv_trainer.model_loader import load_ltxv_components
            import PIL.Image
            import imageio
            
            # 启动性能监控
            monitor = PerformanceMonitor()
            monitor.start_monitoring()
            
            start_time = time.time()
            
            # 设置离线模式
            os.environ["HF_HUB_OFFLINE"] = "1"
            
            # 加载模型组件
            logger.info("加载模型组件...")
            components = load_ltxv_components(
                model_source="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video",
                load_text_encoder_in_8bit=False,
                transformer_dtype=torch.bfloat16,
                vae_dtype=torch.bfloat16,
            )
            
            # 创建管道
            pipeline = LTXConditionPipeline(
                vae=components.vae,
                text_encoder=components.text_encoder,
                tokenizer=components.tokenizer,
                transformer=components.transformer,
                scheduler=components.scheduler,
            )
            
            pipeline = pipeline.to("cuda")
            
            # 启用内存优化
            if hasattr(pipeline, 'enable_model_cpu_offload'):
                pipeline.enable_model_cpu_offload()
            if hasattr(pipeline, 'enable_vae_tiling'):
                pipeline.enable_vae_tiling()
            
            model_load_time = time.time() - start_time
            logger.info(f"模型加载时间: {model_load_time:.1f}秒")
            
            # 准备输入
            cap = cv2.VideoCapture(self.test_video)
            ret, frame = cap.read()
            cap.release()
            
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            condition_image = PIL.Image.fromarray(frame_rgb)
            
            video_condition = LTXVideoCondition(
                image=condition_image,
                frame_index=0,
            )
            
            # 推理
            logger.info("开始推理...")
            inference_start = time.time()
            
            generator = torch.Generator(device="cuda").manual_seed(self.test_params['seed'])
            
            output = pipeline(
                prompt=self.test_prompt,
                conditions=[video_condition],
                width=self.test_params['width'],
                height=self.test_params['height'],
                num_frames=self.test_params['num_frames'],
                generator=generator,
                guidance_scale=self.test_params['guidance_scale'],
                num_inference_steps=self.test_params['num_inference_steps'],
            )
            
            inference_time = time.time() - inference_start
            total_time = time.time() - start_time
            
            # 保存视频
            output_path = self.output_dir / "ltxv_trainer_output.mp4"
            frames = output.frames[0]
            writer = imageio.get_writer(str(output_path), fps=self.test_params['fps'])
            for frame in frames:
                if hasattr(frame, 'convert'):
                    frame_array = np.array(frame.convert('RGB'))
                else:
                    frame_array = frame
                writer.append_data(frame_array)
            writer.close()
            
            # 停止监控
            monitor.stop_monitoring()
            perf_summary = monitor.get_summary()
            
            # 获取文件大小
            file_size = output_path.stat().st_size
            
            result = {
                'method': 'LTX-Video-Trainer',
                'success': True,
                'model_load_time': model_load_time,
                'inference_time': inference_time,
                'total_time': total_time,
                'output_file': str(output_path),
                'file_size_bytes': file_size,
                'num_frames_generated': len(frames),
                'performance': perf_summary,
                'parameters': self.test_params.copy()
            }
            
            logger.info(f"✅ LTX-Video-Trainer测试完成")
            logger.info(f"推理时间: {inference_time:.1f}秒")
            logger.info(f"总时间: {total_time:.1f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"LTX-Video-Trainer测试失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {
                'method': 'LTX-Video-Trainer',
                'success': False,
                'error': str(e),
                'parameters': self.test_params.copy()
            }
    
    def test_ltxv_official_method(self) -> Dict[str, Any]:
        """测试LTX-Video官方方法（无LoRA）"""
        logger.info("=== 测试LTX-Video官方方法 ===")
        
        # 添加路径
        ltxv_path = Path("/data1/wzy/LTXV_ALL/LTX-Video")
        if ltxv_path.exists():
            sys.path.insert(0, str(ltxv_path))
        
        try:
            from ltx_video.inference import infer, InferenceConfig
            
            # 启动性能监控
            monitor = PerformanceMonitor()
            monitor.start_monitoring()
            
            start_time = time.time()
            
            # 创建推理配置
            config = InferenceConfig(
                prompt=self.test_prompt,
                output_path=str(self.output_dir / "ltxv_official"),
                pipeline_config="/data1/wzy/LTXV_ALL/ltxv_2b_fixed_config.yaml",
                height=self.test_params['height'],
                width=self.test_params['width'],
                num_frames=self.test_params['num_frames'],
                conditioning_media_paths=[self.test_video],
                conditioning_start_frames=[0],
                seed=self.test_params['seed'],
                offload_to_cpu=False
            )
            
            logger.info("开始推理...")
            inference_start = time.time()
            
            # 执行推理
            infer(config)
            
            inference_time = time.time() - inference_start
            total_time = time.time() - start_time
            
            # 停止监控
            monitor.stop_monitoring()
            perf_summary = monitor.get_summary()
            
            # 查找输出文件
            output_dir = Path(config.output_path)
            output_files = list(output_dir.glob("*.mp4"))
            
            if output_files:
                output_file = output_files[0]
                file_size = output_file.stat().st_size
                
                # 获取视频帧数
                cap = cv2.VideoCapture(str(output_file))
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                cap.release()
            else:
                output_file = None
                file_size = 0
                frame_count = 0
            
            result = {
                'method': 'LTX-Video-Official',
                'success': True,
                'model_load_time': 0,  # 包含在推理时间中
                'inference_time': inference_time,
                'total_time': total_time,
                'output_file': str(output_file) if output_file else None,
                'file_size_bytes': file_size,
                'num_frames_generated': frame_count,
                'performance': perf_summary,
                'parameters': self.test_params.copy()
            }
            
            logger.info(f"✅ LTX-Video官方测试完成")
            logger.info(f"推理时间: {inference_time:.1f}秒")
            logger.info(f"总时间: {total_time:.1f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"LTX-Video官方测试失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {
                'method': 'LTX-Video-Official',
                'success': False,
                'error': str(e),
                'parameters': self.test_params.copy()
            }
    
    def run_comparison(self) -> Dict[str, Any]:
        """运行完整对比测试"""
        logger.info("开始推理性能对比测试...")
        
        results = {}
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 测试1：LTX-Video-Trainer方法
        results['ltxv_trainer'] = self.test_ltxv_trainer_method()
        
        # 清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 等待一段时间确保内存清理
        time.sleep(5)
        
        # 测试2：LTX-Video官方方法
        results['ltxv_official'] = self.test_ltxv_official_method()
        
        # 生成对比报告
        self.generate_comparison_report(results)
        
        return results
    
    def generate_comparison_report(self, results: Dict[str, Any]):
        """生成对比报告"""
        report_path = self.output_dir / "performance_comparison_report.json"
        
        # 保存详细结果
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # 生成Markdown报告
        md_report_path = self.output_dir / "performance_comparison_report.md"
        
        with open(md_report_path, 'w', encoding='utf-8') as f:
            f.write("# LTX-Video推理性能对比报告\n\n")
            f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 测试配置\n")
            f.write(f"- **测试视频**: {self.test_video}\n")
            f.write(f"- **测试提示词**: {self.test_prompt}\n")
            f.write("- **测试参数**:\n")
            for key, value in self.test_params.items():
                f.write(f"  - {key}: {value}\n")
            f.write("\n")
            
            f.write("## 性能对比结果\n\n")
            f.write("| 指标 | LTX-Video-Trainer | LTX-Video-Official | 差异 |\n")
            f.write("|------|-------------------|-------------------|------|\n")
            
            if results['ltxv_trainer']['success'] and results['ltxv_official']['success']:
                trainer_time = results['ltxv_trainer']['inference_time']
                official_time = results['ltxv_official']['inference_time']
                time_ratio = trainer_time / official_time if official_time > 0 else 0
                
                trainer_memory = results['ltxv_trainer']['performance'].get('gpu_memory_peak_gb', 0)
                official_memory = results['ltxv_official']['performance'].get('gpu_memory_peak_gb', 0)
                
                trainer_size = results['ltxv_trainer']['file_size_bytes']
                official_size = results['ltxv_official']['file_size_bytes']
                
                f.write(f"| 推理时间 | {trainer_time:.1f}秒 | {official_time:.1f}秒 | {time_ratio:.1f}x |\n")
                f.write(f"| GPU内存峰值 | {trainer_memory:.1f}GB | {official_memory:.1f}GB | {trainer_memory-official_memory:+.1f}GB |\n")
                f.write(f"| 输出文件大小 | {trainer_size:,}字节 | {official_size:,}字节 | {trainer_size-official_size:+,}字节 |\n")
                f.write(f"| 生成帧数 | {results['ltxv_trainer']['num_frames_generated']} | {results['ltxv_official']['num_frames_generated']} | - |\n")
            
            f.write("\n## 详细分析\n")
            
            if results['ltxv_trainer']['success']:
                f.write("### LTX-Video-Trainer方法\n")
                f.write("- ✅ 测试成功\n")
                f.write(f"- 模型加载时间: {results['ltxv_trainer']['model_load_time']:.1f}秒\n")
                f.write(f"- 推理时间: {results['ltxv_trainer']['inference_time']:.1f}秒\n")
                f.write(f"- 总时间: {results['ltxv_trainer']['total_time']:.1f}秒\n\n")
            else:
                f.write("### LTX-Video-Trainer方法\n")
                f.write("- ❌ 测试失败\n")
                f.write(f"- 错误: {results['ltxv_trainer'].get('error', 'Unknown')}\n\n")
            
            if results['ltxv_official']['success']:
                f.write("### LTX-Video官方方法\n")
                f.write("- ✅ 测试成功\n")
                f.write(f"- 推理时间: {results['ltxv_official']['inference_time']:.1f}秒\n")
                f.write(f"- 总时间: {results['ltxv_official']['total_time']:.1f}秒\n\n")
            else:
                f.write("### LTX-Video官方方法\n")
                f.write("- ❌ 测试失败\n")
                f.write(f"- 错误: {results['ltxv_official'].get('error', 'Unknown')}\n\n")
        
        logger.info(f"✅ 对比报告已生成: {md_report_path}")

def main():
    """主函数"""
    comparator = InferenceComparator()
    results = comparator.run_comparison()
    
    logger.info("🎉 性能对比测试完成！")
    logger.info(f"详细报告请查看: {comparator.output_dir}")

if __name__ == "__main__":
    main()

# LoRA训练效果评估脚本使用指南

本文档介绍如何使用LoRA训练效果评估脚本来自动化测试不同checkpoint的视频生成质量。

## 概述

我们提供了五个版本的评估脚本：

1. **evaluate_lora_checkpoints.py** - 完整版本，支持ComfyUI工作流
2. **evaluate_lora_simple.py** - 简化版本，基于LTX-Video原生inference.py
3. **evaluate_lora_comfyui.py** - ComfyUI专用版本，支持API调用
4. **evaluate_lora_improved.py** - ⭐ **推荐版本**，基于深入代码分析的改进版
5. **evaluate_lora_comfyui_improved.py** - ComfyUI改进版，使用正确的LTX-Video节点

## 功能特性

- ✅ 自动遍历checkpoints目录中的所有checkpoint文件
- ✅ 从测试视频中提取首帧作为参考图像
- ✅ 从prompt.txt中读取对应的文本提示词
- ✅ 批量处理多个checkpoint和测试视频
- ✅ 生成有组织的输出目录结构
- ✅ 支持LoRA格式转换（Diffusers -> ComfyUI）
- ✅ 包含错误处理和进度显示
- ✅ 生成详细的评估报告

## 目录结构

```
/data1/wzy/LTXV_ALL/
├── outputs/ltxv_2b_lora_airscape/checkpoints/    # LoRA checkpoints
│   ├── lora_weights_step_00600.safetensors
│   ├── lora_weights_step_00900.safetensors
│   └── ...
├── LTXV_models/LTX-Video/                         # 基础模型
├── demo_video/airscape/                           # 测试视频
│   ├── 00819_urbanvideo_test.mp4
│   ├── 00840_urbanvideo_test.mp4
│   └── ...
├── demo_video/prompt.txt                          # 提示词文件
└── lora_evaluation_results/                       # 输出目录（自动创建）
```

## 使用方法

### 1. 改进版本（⭐ 强烈推荐）

基于深入代码分析，使用正确的LTX-Video-Trainer API：

```bash
python evaluate_lora_improved.py \
    --checkpoints-dir /data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints \
    --base-model-path /data1/wzy/LTXV_ALL/LTXV_models/LTX-Video \
    --test-videos-dir /data1/wzy/LTXV_ALL/demo_video/airscape \
    --prompt-file /data1/wzy/LTXV_ALL/demo_video/prompt.txt \
    --output-dir /data1/wzy/LTXV_ALL/lora_evaluation_results_improved
```

**改进版本的优势：**
- ✅ 使用正确的`LTXConditionPipeline`
- ✅ 正确的LoRA加载机制（`LTXVideoLoraLoaderMixin`）
- ✅ 与训练时配置完全一致
- ✅ 官方推荐的推理方式

### 2. ComfyUI改进版本

使用正确的LTX-Video ComfyUI节点：

```bash
python evaluate_lora_comfyui_improved.py \
    --checkpoints-dir /data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints \
    --base-model-path /data1/wzy/LTXV_ALL/LTXV_models/LTX-Video \
    --test-videos-dir /data1/wzy/LTXV_ALL/demo_video/airscape \
    --prompt-file /data1/wzy/LTXV_ALL/demo_video/prompt.txt \
    --output-dir /data1/wzy/LTXV_ALL/lora_evaluation_results_comfyui \
    --comfyui-path /path/to/ComfyUI
```

### 3. 简化版本（备选）

基于LTX-Video原生inference.py：

```bash
python evaluate_lora_simple.py \
    --checkpoints-dir /data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints \
    --base-model-path /data1/wzy/LTXV_ALL/LTXV_models/LTX-Video \
    --test-videos-dir /data1/wzy/LTXV_ALL/demo_video/airscape \
    --prompt-file /data1/wzy/LTXV_ALL/demo_video/prompt.txt \
    --output-dir /data1/wzy/LTXV_ALL/lora_evaluation_results \
    --ltx-video-path /data1/wzy/LTXV_ALL/LTX-Video
```

### 4. 仅检查配置（Dry Run）

在实际运行前检查配置是否正确：

```bash
python evaluate_lora_improved.py --dry-run --verbose
```

## 输出结构

评估完成后，输出目录结构如下：

```
lora_evaluation_results/
├── lora_weights_step_00600/
│   ├── 00819_urbanvideo_test_first_frame.jpg
│   ├── 00819_urbanvideo_test_generated.mp4
│   ├── 00819_urbanvideo_test_metadata.json
│   ├── 00840_urbanvideo_test_first_frame.jpg
│   ├── 00840_urbanvideo_test_generated.mp4
│   └── ...
├── lora_weights_step_00900/
│   └── ...
├── evaluation_report.md
└── lora_evaluation_simple.log
```

## 配置参数

### 必需参数

- `--checkpoints-dir`: LoRA checkpoints目录路径
- `--base-model-path`: 基础模型路径
- `--test-videos-dir`: 测试视频目录路径
- `--prompt-file`: 提示词文件路径
- `--output-dir`: 输出目录路径

### 可选参数

- `--ltx-video-path`: LTX-Video项目路径（简化版本需要）
- `--comfyui-path`: ComfyUI安装路径（ComfyUI版本需要）
- `--dry-run`: 仅检查配置，不执行评估
- `--verbose`: 详细输出

## 提示词文件格式

prompt.txt文件应该是表格格式，包含视频文件名和对应的运动描述：

```
| Example |                                          Prediction                                           |                                                                                                                             Motion Intention                                                                                                                              |
|:-------:|:---------------------------------------------------------------------------------------------:|:---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| **1** |         <img src="assets/generated_example/00819_urbanvideo_test.gif" width="100%"/>          |                                                      The drone moved forward with its camera pointed straight ahead, capturing a stationary view of high-rise buildings, a landscaped garden, and a pond.                                                       |
| **2** |         <img src="assets/generated_example/00840_urbanvideo_test.gif" width="100%"/>          |                             The drone rotated counterclockwise inplace, with its camera gimbal angled downward, and concluded its flight above a courtyard featuring a circular fountain, swimming pools, and surrounding greenery.                             |
```

## 内存优化

脚本包含以下内存优化策略：

- 使用FP16精度
- 启用CPU卸载
- 启用VAE分块
- 单GPU推理（避免多GPU通信开销）

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少batch_size
   - 启用更多内存优化选项
   - 使用较小的分辨率

2. **checkpoint转换失败**
   - 检查convert_checkpoint.py脚本是否存在
   - 确保有足够的磁盘空间
   - 检查文件权限

3. **ComfyUI连接失败**
   - 确保ComfyUI服务正在运行
   - 检查API端口（默认8188）
   - 验证网络连接

### 日志文件

脚本会生成详细的日志文件：
- `lora_evaluation_simple.log` - 简化版本日志
- `lora_evaluation_comfyui.log` - ComfyUI版本日志

## 性能建议

1. **GPU使用**：建议使用单张高性能GPU（如A800）
2. **存储**：确保有足够的磁盘空间存储生成的视频
3. **网络**：如果使用ComfyUI API，确保网络连接稳定
4. **并发**：避免同时运行多个评估任务

## 改进版本详细说明

### 基于代码分析的关键改进

通过深入分析LTX-Video-Trainer项目代码，我们发现了以下关键问题并进行了改进：

#### 1. **LoRA加载机制问题**
- **问题**：原版本使用通用的推理方式，不支持LoRA权重加载
- **改进**：使用`LTXVideoLoraLoaderMixin.load_lora_weights()`方法
- **代码位置**：`LTX-Video-Trainer/src/ltxv_trainer/ltxv_pipeline.py:226`

#### 2. **推理管道问题**
- **问题**：使用LTX-Video原生`inference.py`，不支持LoRA
- **改进**：使用`LTXConditionPipeline`，这是官方推荐的推理管道
- **代码位置**：`LTX-Video-Trainer/src/ltxv_trainer/trainer.py:797-802`

#### 3. **模型配置一致性**
- **问题**：推理时的配置与训练时不一致
- **改进**：使用与训练时相同的模型版本、精度设置和参数
- **配置文件**：`LTX-Video-Trainer/configs/ltxv_2b_lora.yaml`

#### 4. **ComfyUI工作流问题**
- **问题**：使用通用的图像生成节点，不是视频生成
- **改进**：使用LTX-Video专用的ComfyUI节点
- **节点类型**：`LTXV Model Loader`, `LTXV LoRA Loader`, `LTXV Video Condition`

### 技术细节

```python
# 正确的LoRA加载方式（改进版本）
self.pipeline.load_lora_weights(lora_path)
self.pipeline.set_adapters_scale(1.0)

# 正确的推理调用
output = self.pipeline(
    prompt=prompt,
    video_condition=video_condition,
    width=1216,
    height=704,
    num_frames=121,
    guidance_scale=3.0,  # 与训练配置一致
    num_inference_steps=40
)
```

## 扩展功能

脚本设计为模块化，可以轻松扩展：

- 添加新的视频质量评估指标
- 支持更多的模型格式
- 集成其他推理后端
- 添加自动化报告生成

## 注意事项

1. **GPU占用**：评估过程会占用GPU资源，确保没有其他训练任务在运行
2. **文件权限**：确保脚本有读写相关目录的权限
3. **依赖环境**：确保已安装所有必需的Python包
4. **模型兼容性**：确保LoRA checkpoint与基础模型兼容
5. **版本一致性**：改进版本需要LTX-Video-Trainer环境

## 联系支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. 确认所有路径和文件都存在
3. 验证环境配置是否正确
4. 确保LTX-Video-Trainer正确安装

评估完成后，可以通过生成的报告和视频文件来比较不同checkpoint的效果。

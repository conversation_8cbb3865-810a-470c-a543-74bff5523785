#!/usr/bin/env python3
"""
LoRA训练效果评估脚本

该脚本用于自动化评估不同checkpoint的视频生成质量，支持：
1. 自动遍历checkpoints目录中的所有checkpoint文件
2. 从测试视频中提取首帧作为参考图像
3. 从prompt.txt中读取对应的文本提示词
4. 使用ComfyUI工作流生成视频
5. 将生成的视频保存到有组织的输出目录中

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import json
import cv2
import argparse
import subprocess
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import logging
from tqdm import tqdm
import re
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lora_evaluation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class EvaluationConfig:
    """评估配置类"""
    checkpoints_dir: str
    base_model_path: str
    test_videos_dir: str
    prompt_file: str
    output_dir: str
    comfyui_path: str
    workflow_template: str
    
    # 生成参数
    width: int = 1216
    height: int = 704
    num_frames: int = 121
    fps: int = 30
    seed: int = 42
    
    # 内存优化
    use_fp16: bool = True
    enable_cpu_offload: bool = True
    enable_vae_tiling: bool = True

class LoRAEvaluator:
    """LoRA评估器主类"""
    
    def __init__(self, config: EvaluationConfig):
        self.config = config
        self.checkpoints = []
        self.test_videos = []
        self.prompts = {}
        
        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)
        
    def setup(self) -> bool:
        """初始化设置"""
        logger.info("开始初始化评估环境...")
        
        # 检查必要路径
        if not self._check_paths():
            return False
            
        # 扫描checkpoints
        if not self._scan_checkpoints():
            return False
            
        # 扫描测试视频
        if not self._scan_test_videos():
            return False
            
        # 加载提示词
        if not self._load_prompts():
            return False
            
        logger.info(f"找到 {len(self.checkpoints)} 个checkpoints")
        logger.info(f"找到 {len(self.test_videos)} 个测试视频")
        logger.info(f"加载了 {len(self.prompts)} 个提示词")
        
        return True
    
    def _check_paths(self) -> bool:
        """检查必要路径是否存在"""
        paths_to_check = [
            (self.config.checkpoints_dir, "Checkpoints目录"),
            (self.config.base_model_path, "基础模型路径"),
            (self.config.test_videos_dir, "测试视频目录"),
            (self.config.prompt_file, "提示词文件"),
            (self.config.comfyui_path, "ComfyUI路径")
        ]
        
        for path, name in paths_to_check:
            if not os.path.exists(path):
                logger.error(f"{name} 不存在: {path}")
                return False
                
        return True
    
    def _scan_checkpoints(self) -> bool:
        """扫描checkpoint文件"""
        checkpoints_dir = Path(self.config.checkpoints_dir)
        
        # 查找所有.safetensors文件
        checkpoint_files = list(checkpoints_dir.glob("*.safetensors"))
        
        if not checkpoint_files:
            logger.error(f"在 {checkpoints_dir} 中未找到任何.safetensors文件")
            return False
        
        # 按步数排序
        def extract_step_number(filename: str) -> int:
            match = re.search(r'step_(\d+)', filename)
            return int(match.group(1)) if match else 0
        
        checkpoint_files.sort(key=lambda x: extract_step_number(x.name))
        
        self.checkpoints = [
            {
                'path': str(checkpoint_file),
                'name': checkpoint_file.stem,
                'step': extract_step_number(checkpoint_file.name)
            }
            for checkpoint_file in checkpoint_files
        ]
        
        return True
    
    def _scan_test_videos(self) -> bool:
        """扫描测试视频文件"""
        test_videos_dir = Path(self.config.test_videos_dir)
        
        # 查找所有.mp4文件
        video_files = list(test_videos_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error(f"在 {test_videos_dir} 中未找到任何.mp4文件")
            return False
        
        self.test_videos = [
            {
                'path': str(video_file),
                'name': video_file.stem,
                'filename': video_file.name
            }
            for video_file in video_files
        ]
        
        return True
    
    def _load_prompts(self) -> bool:
        """加载提示词文件"""
        try:
            with open(self.config.prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析表格格式的提示词
            lines = content.strip().split('\n')
            for line in lines[2:]:  # 跳过表头
                if '|' in line:
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 4:
                        example_num = parts[1]
                        motion_description = parts[3]
                        
                        # 提取视频文件名模式
                        if 'urbanvideo_test' in motion_description or '00819' in example_num:
                            self.prompts['00819_urbanvideo_test'] = motion_description
                        elif '00840' in example_num:
                            self.prompts['00840_urbanvideo_test'] = motion_description
                        elif '00846' in example_num:
                            self.prompts['00846_urbanvideo_test'] = motion_description
                        elif '01035' in example_num:
                            self.prompts['01035_NAT2021_test_N02029_4'] = motion_description
                        elif '01374' in example_num:
                            self.prompts['01374_NAT2021_test_N08024_3'] = motion_description
            
            return len(self.prompts) > 0
            
        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
            return False
    
    def extract_first_frame(self, video_path: str, output_path: str) -> bool:
        """从视频中提取首帧"""
        try:
            cap = cv2.VideoCapture(video_path)
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                cv2.imwrite(output_path, frame)
                return True
            else:
                logger.error(f"无法从视频中读取帧: {video_path}")
                return False
                
        except Exception as e:
            logger.error(f"提取首帧失败: {e}")
            return False
    
    def convert_checkpoint_to_comfyui(self, checkpoint_path: str) -> str:
        """将checkpoint转换为ComfyUI格式"""
        checkpoint_path = Path(checkpoint_path)
        comfyui_path = checkpoint_path.parent / f"{checkpoint_path.stem}_comfy.safetensors"
        
        # 如果已经存在ComfyUI格式文件，直接返回
        if comfyui_path.exists():
            return str(comfyui_path)
        
        # 使用转换脚本
        convert_script = Path(self.config.base_model_path).parent / "LTX-Video-Trainer" / "scripts" / "convert_checkpoint.py"
        
        if not convert_script.exists():
            logger.error(f"转换脚本不存在: {convert_script}")
            return None
        
        try:
            cmd = [
                sys.executable, str(convert_script),
                str(checkpoint_path),
                "--to-comfy",
                "--output-path", str(comfyui_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"成功转换checkpoint: {checkpoint_path.name} -> {comfyui_path.name}")
            return str(comfyui_path)
            
        except subprocess.CalledProcessError as e:
            logger.error(f"转换checkpoint失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            return None

    def create_comfyui_workflow(self, lora_path: str, image_path: str, prompt: str, output_path: str) -> Dict:
        """创建ComfyUI工作流JSON"""

        # 基础工作流模板
        workflow = {
            "1": {
                "inputs": {
                    "ckpt_name": "ltxv-2b-0.9.6-dev-04-25.safetensors"
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load Checkpoint"}
            },
            "2": {
                "inputs": {
                    "text": prompt,
                    "clip": ["1", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Prompt)"}
            },
            "3": {
                "inputs": {
                    "text": "",
                    "clip": ["1", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Negative)"}
            },
            "4": {
                "inputs": {
                    "image": image_path,
                    "upload": "image"
                },
                "class_type": "LoadImage",
                "_meta": {"title": "Load Image"}
            },
            "5": {
                "inputs": {
                    "lora_name": os.path.basename(lora_path),
                    "strength_model": 1.0,
                    "strength_clip": 1.0,
                    "model": ["1", 0],
                    "clip": ["1", 1]
                },
                "class_type": "LoraLoader",
                "_meta": {"title": "Load LoRA"}
            },
            "6": {
                "inputs": {
                    "width": self.config.width,
                    "height": self.config.height,
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Empty Latent Image"}
            },
            "7": {
                "inputs": {
                    "seed": self.config.seed,
                    "steps": 40,
                    "cfg": 3.0,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 1.0,
                    "model": ["5", 0],
                    "positive": ["2", 0],
                    "negative": ["3", 0],
                    "latent_image": ["6", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "KSampler"}
            },
            "8": {
                "inputs": {
                    "samples": ["7", 0],
                    "vae": ["1", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE Decode"}
            },
            "9": {
                "inputs": {
                    "filename_prefix": "ltxv_lora_output",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage",
                "_meta": {"title": "Save Image"}
            }
        }

        return workflow

    def run_comfyui_workflow(self, workflow: Dict, output_video_path: str) -> bool:
        """运行ComfyUI工作流"""
        try:
            # 保存工作流到临时文件
            workflow_file = Path(self.config.output_dir) / "temp_workflow.json"
            with open(workflow_file, 'w', encoding='utf-8') as f:
                json.dump(workflow, f, indent=2)

            # 构建ComfyUI命令
            cmd = [
                sys.executable,
                os.path.join(self.config.comfyui_path, "main.py"),
                "--input", str(workflow_file),
                "--output-directory", os.path.dirname(output_video_path)
            ]

            # 运行ComfyUI
            logger.info(f"运行ComfyUI工作流...")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600,  # 10分钟超时
                cwd=self.config.comfyui_path
            )

            if result.returncode == 0:
                logger.info("ComfyUI工作流执行成功")
                return True
            else:
                logger.error(f"ComfyUI工作流执行失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("ComfyUI工作流执行超时")
            return False
        except Exception as e:
            logger.error(f"运行ComfyUI工作流时出错: {e}")
            return False
        finally:
            # 清理临时文件
            if workflow_file.exists():
                workflow_file.unlink()

    def evaluate_single_checkpoint(self, checkpoint: Dict, test_video: Dict) -> bool:
        """评估单个checkpoint"""
        checkpoint_name = checkpoint['name']
        video_name = test_video['name']

        logger.info(f"评估 {checkpoint_name} 使用测试视频 {video_name}")

        # 创建输出目录
        output_dir = Path(self.config.output_dir) / checkpoint_name
        output_dir.mkdir(exist_ok=True)

        # 提取首帧
        first_frame_path = output_dir / f"{video_name}_first_frame.jpg"
        if not self.extract_first_frame(test_video['path'], str(first_frame_path)):
            return False

        # 获取对应的提示词
        prompt = self.prompts.get(video_name, "A drone flying over a landscape")

        # 转换checkpoint为ComfyUI格式
        comfyui_checkpoint = self.convert_checkpoint_to_comfyui(checkpoint['path'])
        if not comfyui_checkpoint:
            return False

        # 创建工作流
        output_video_path = output_dir / f"{video_name}_generated.mp4"
        workflow = self.create_comfyui_workflow(
            comfyui_checkpoint,
            str(first_frame_path),
            prompt,
            str(output_video_path)
        )

        # 运行工作流
        success = self.run_comfyui_workflow(workflow, str(output_video_path))

        if success:
            logger.info(f"成功生成视频: {output_video_path}")
        else:
            logger.error(f"生成视频失败: {output_video_path}")

        return success

    def run_evaluation(self) -> None:
        """运行完整评估"""
        logger.info("开始LoRA checkpoint评估...")

        total_tasks = len(self.checkpoints) * len(self.test_videos)
        completed_tasks = 0
        failed_tasks = 0

        # 创建进度条
        with tqdm(total=total_tasks, desc="评估进度") as pbar:
            for checkpoint in self.checkpoints:
                for test_video in self.test_videos:
                    try:
                        success = self.evaluate_single_checkpoint(checkpoint, test_video)
                        if success:
                            completed_tasks += 1
                        else:
                            failed_tasks += 1
                    except Exception as e:
                        logger.error(f"评估过程中出现异常: {e}")
                        failed_tasks += 1

                    pbar.update(1)
                    pbar.set_postfix({
                        'completed': completed_tasks,
                        'failed': failed_tasks
                    })

        # 生成评估报告
        self.generate_report(completed_tasks, failed_tasks, total_tasks)

    def generate_report(self, completed: int, failed: int, total: int) -> None:
        """生成评估报告"""
        report_path = Path(self.config.output_dir) / "evaluation_report.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# LoRA Checkpoint 评估报告\n\n")
            f.write(f"**评估时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**总任务数**: {total}\n")
            f.write(f"**成功完成**: {completed}\n")
            f.write(f"**失败任务**: {failed}\n")
            f.write(f"**成功率**: {completed/total*100:.1f}%\n\n")

            f.write("## Checkpoints\n\n")
            for checkpoint in self.checkpoints:
                f.write(f"- {checkpoint['name']} (Step: {checkpoint['step']})\n")

            f.write("\n## 测试视频\n\n")
            for video in self.test_videos:
                f.write(f"- {video['name']}\n")
                prompt = self.prompts.get(video['name'], "无对应提示词")
                f.write(f"  - 提示词: {prompt}\n")

            f.write("\n## 输出结构\n\n")
            f.write("```\n")
            f.write(f"{self.config.output_dir}/\n")
            for checkpoint in self.checkpoints:
                f.write(f"├── {checkpoint['name']}/\n")
                for video in self.test_videos:
                    f.write(f"│   ├── {video['name']}_first_frame.jpg\n")
                    f.write(f"│   └── {video['name']}_generated.mp4\n")
            f.write("└── evaluation_report.md\n")
            f.write("```\n")

        logger.info(f"评估报告已生成: {report_path}")


def create_default_config() -> EvaluationConfig:
    """创建默认配置"""
    return EvaluationConfig(
        checkpoints_dir="/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints",
        base_model_path="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video",
        test_videos_dir="/data1/wzy/LTXV_ALL/demo_video/airscape",
        prompt_file="/data1/wzy/LTXV_ALL/demo_video/prompt.txt",
        output_dir="/data1/wzy/LTXV_ALL/lora_evaluation_results",
        comfyui_path="/path/to/ComfyUI",  # 需要用户指定
        workflow_template="ltxv-2b-i2v-base.json"
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LoRA训练效果评估脚本")

    parser.add_argument("--checkpoints-dir",
                       default="/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints",
                       help="LoRA checkpoints目录路径")

    parser.add_argument("--base-model-path",
                       default="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video",
                       help="基础模型路径")

    parser.add_argument("--test-videos-dir",
                       default="/data1/wzy/LTXV_ALL/demo_video/airscape",
                       help="测试视频目录路径")

    parser.add_argument("--prompt-file",
                       default="/data1/wzy/LTXV_ALL/demo_video/prompt.txt",
                       help="提示词文件路径")

    parser.add_argument("--output-dir",
                       default="/data1/wzy/LTXV_ALL/lora_evaluation_results",
                       help="输出目录路径")

    parser.add_argument("--comfyui-path",
                       required=True,
                       help="ComfyUI安装路径")

    parser.add_argument("--width", type=int, default=1216, help="生成视频宽度")
    parser.add_argument("--height", type=int, default=704, help="生成视频高度")
    parser.add_argument("--num-frames", type=int, default=121, help="生成视频帧数")
    parser.add_argument("--fps", type=int, default=30, help="生成视频帧率")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")

    parser.add_argument("--dry-run", action="store_true", help="仅检查配置，不执行评估")
    parser.add_argument("--verbose", action="store_true", help="详细输出")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 创建配置
    config = EvaluationConfig(
        checkpoints_dir=args.checkpoints_dir,
        base_model_path=args.base_model_path,
        test_videos_dir=args.test_videos_dir,
        prompt_file=args.prompt_file,
        output_dir=args.output_dir,
        comfyui_path=args.comfyui_path,
        workflow_template="ltxv-2b-i2v-base.json",
        width=args.width,
        height=args.height,
        num_frames=args.num_frames,
        fps=args.fps,
        seed=args.seed
    )

    # 创建评估器
    evaluator = LoRAEvaluator(config)

    # 初始化
    if not evaluator.setup():
        logger.error("初始化失败，退出程序")
        sys.exit(1)

    if args.dry_run:
        logger.info("Dry run模式，配置检查完成")
        return

    # 运行评估
    try:
        evaluator.run_evaluation()
        logger.info("评估完成！")
    except KeyboardInterrupt:
        logger.info("用户中断评估")
    except Exception as e:
        logger.error(f"评估过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

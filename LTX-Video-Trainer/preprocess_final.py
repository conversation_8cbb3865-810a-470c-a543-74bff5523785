#!/usr/bin/env python3
"""
最终的预处理脚本 - 确保完全离线运行
"""

# 必须在任何其他导入之前设置环境变量
import os
os.environ["HF_HUB_OFFLINE"] = "1"
os.environ["TRANSFORMERS_OFFLINE"] = "1"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

import sys
import json
import subprocess
from pathlib import Path

def main():
    print("开始最终预处理...")
    print(f"HF_HUB_OFFLINE: {os.environ.get('HF_HUB_OFFLINE')}")
    print(f"TRANSFORMERS_OFFLINE: {os.environ.get('TRANSFORMERS_OFFLINE')}")
    print(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES')}")
    print("=" * 60)
    
    # 数据集路径
    dataset_path = "/data1/wzy/LTXV_ALL/LTX-Video-Trainer/airscape/dataset.json"
    output_dir = "/data1/wzy/LTXV_ALL/LTX-Video-Trainer/airscape/.precomputed"
    
    # 检查数据集文件
    if not Path(dataset_path).exists():
        print(f"❌ 数据集文件不存在: {dataset_path}")
        return 1
    
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 构建命令 - 使用python -c来确保环境变量在正确的时机设置
    python_code = f'''
import os
os.environ["HF_HUB_OFFLINE"] = "1"
os.environ["TRANSFORMERS_OFFLINE"] = "1"

import sys
sys.path.insert(0, "src")

from scripts.preprocess_dataset import main as preprocess_main
import typer

# 模拟命令行参数
sys.argv = [
    "preprocess_dataset.py",
    "{dataset_path}",
    "--resolution-buckets", "768x448x89",
    "--caption-column", "caption",
    "--video-column", "media_path", 
    "--model-source", "LTXV_2B_0.9.6_DEV",
    "--batch-size", "1",
    "--output-dir", "{output_dir}"
]

# 运行预处理
try:
    app = typer.Typer()
    app.command()(preprocess_main)
    app()
except SystemExit as e:
    if e.code == 0:
        print("预处理成功完成")
    else:
        print(f"预处理失败，退出码: {{e.code}}")
        sys.exit(e.code)
'''
    
    print("执行预处理...")
    print("-" * 60)
    
    try:
        # 运行Python代码
        result = subprocess.run([
            sys.executable, "-c", python_code
        ], 
        cwd="/data1/wzy/LTXV_ALL/LTX-Video-Trainer",
        check=True,
        text=True,
        capture_output=False  # 实时显示输出
        )
        
        print("\n" + "=" * 60)
        print("✅ 预处理完成!")
        
        # 检查输出
        if Path(output_dir).exists():
            subdirs = [d for d in Path(output_dir).iterdir() if d.is_dir()]
            print(f"生成的子目录: {[d.name for d in subdirs]}")
            
            for subdir in subdirs:
                file_count = len(list(subdir.glob("*")))
                print(f"  {subdir.name}: {file_count} 个文件")
        
        return 0
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 预处理失败: {e}")
        return 1
    except Exception as e:
        print(f"\n❌ 执行错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

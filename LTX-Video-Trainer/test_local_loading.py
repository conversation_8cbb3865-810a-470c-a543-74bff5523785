#!/usr/bin/env python3
"""
测试本地模型加载是否正常工作
"""

import os
import sys
from pathlib import Path

# 设置离线模式
os.environ["HF_HUB_OFFLINE"] = "1"
os.environ["TRANSFORMERS_OFFLINE"] = "1"

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ltxv_trainer.model_loader import load_tokenizer, load_text_encoder, load_vae

def test_tokenizer():
    print("测试tokenizer加载...")
    try:
        tokenizer = load_tokenizer()
        print("✅ tokenizer加载成功")
        return True
    except Exception as e:
        print(f"❌ tokenizer加载失败: {e}")
        return False

def test_text_encoder():
    print("测试text_encoder加载...")
    try:
        text_encoder = load_text_encoder(load_in_8bit=True)
        print("✅ text_encoder加载成功")
        return True
    except Exception as e:
        print(f"❌ text_encoder加载失败: {e}")
        return False

def test_vae():
    print("测试VAE加载...")
    try:
        vae = load_vae("LTXV_2B_0.9.6_DEV")
        print("✅ VAE加载成功")
        return True
    except Exception as e:
        print(f"❌ VAE加载失败: {e}")
        return False

def main():
    print("开始测试本地模型加载...")
    print(f"HF_HUB_OFFLINE: {os.environ.get('HF_HUB_OFFLINE')}")
    print(f"TRANSFORMERS_OFFLINE: {os.environ.get('TRANSFORMERS_OFFLINE')}")
    print("-" * 50)
    
    results = []
    results.append(test_tokenizer())
    results.append(test_text_encoder())
    results.append(test_vae())
    
    print("-" * 50)
    if all(results):
        print("✅ 所有组件加载成功！")
        return 0
    else:
        print("❌ 部分组件加载失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())

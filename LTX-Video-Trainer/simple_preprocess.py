#!/usr/bin/env python3
"""
简化的预处理脚本 - 直接使用修改后的模型加载器
"""

import os
import sys
import json
from pathlib import Path

# 必须在任何其他导入之前设置离线模式
os.environ["HF_HUB_OFFLINE"] = "1"
os.environ["TRANSFORMERS_OFFLINE"] = "1"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

import torch
from ltxv_trainer.model_loader import load_tokenizer, load_text_encoder, load_vae
from ltxv_trainer.ltxv_utils import encode_prompt

def test_caption_processing():
    """测试caption处理"""
    print("开始测试caption处理...")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载模型组件
    print("加载tokenizer...")
    tokenizer = load_tokenizer()
    print("✅ tokenizer加载成功")
    
    print("加载text_encoder...")
    text_encoder = load_text_encoder(load_in_8bit=True)
    # 8bit模型已经在正确的设备上，不需要调用.to()
    print("✅ text_encoder加载成功")
    
    # 测试编码
    test_caption = "The video is egocentric/first-person perspective, captured from a camera mounted on a drone. The drone moves forward above the road."
    print(f"测试编码: {test_caption[:50]}...")
    
    # 使用encode_prompt函数
    result = encode_prompt(
        tokenizer=tokenizer,
        text_encoder=text_encoder,
        prompt=test_caption,
        device=device,
        max_sequence_length=512
    )

    print(f"✅ 编码成功! 结果包含: {list(result.keys())}")
    
    # 清理GPU内存
    del text_encoder
    torch.cuda.empty_cache()
    
    return True

def test_video_processing():
    """测试视频处理"""
    print("\n开始测试视频处理...")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载VAE
    print("加载VAE...")
    vae = load_vae("LTXV_2B_0.9.6_DEV").to(device)
    print("✅ VAE加载成功")
    
    # 清理GPU内存
    del vae
    torch.cuda.empty_cache()
    
    return True

def run_simple_preprocessing():
    """运行简化的预处理流程"""
    print("开始简化预处理流程...")
    
    # 读取数据集
    dataset_path = "/data1/wzy/LTXV_ALL/LTX-Video-Trainer/airscape/dataset.json"
    with open(dataset_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    print(f"数据集包含 {len(dataset)} 个样本")
    
    # 测试前几个样本
    for i, item in enumerate(dataset[:3]):
        print(f"\n处理样本 {i+1}: {item['caption'][:50]}...")
        video_path = item['media_path']
        if Path(video_path).exists():
            print(f"✅ 视频文件存在: {Path(video_path).name}")
        else:
            print(f"❌ 视频文件不存在: {video_path}")
    
    return True

def main():
    print("简化预处理测试")
    print(f"HF_HUB_OFFLINE: {os.environ.get('HF_HUB_OFFLINE')}")
    print(f"TRANSFORMERS_OFFLINE: {os.environ.get('TRANSFORMERS_OFFLINE')}")
    print(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES')}")
    print("=" * 60)
    
    try:
        # 测试数据集读取
        run_simple_preprocessing()
        
        # 测试caption处理
        test_caption_processing()
        
        # 测试视频处理
        test_video_processing()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！模型加载正常工作")
        print("现在可以运行完整的预处理流程")
        return 0
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python

"""
Launch distributed training for LTXV models using Accelerate.

This script sets up distributed training across multiple GPUs on a single host
using Hugging Face Accelerate. It handles launching the training process with the
appropriate configuration.

Basic usage:
    distributed_train.py configs/ltxv_lora_config.yaml
"""

import subprocess
from pathlib import Path

import click

from accelerate.commands.launch import launch_command, launch_command_parser

from ltxv_trainer import logger


@click.command(help="Launch distributed training for LTXV")
@click.argument("config", type=click.Path(exists=True), required=True)
@click.option(
    "--num_processes",
    type=int,
    help="Number of processes/GPUs to use (overrides accelerate config)",
)
@click.option(
    "--mixed_precision",
    type=str,
    default="bf16",
    help="Mixed precision mode (fp16, bf16, no)",
)
@click.option(
    "--dynamo_backend",
    type=str,
    default="no",
    help="Dynamo backend to use",
)
def main(
    config: str,
    num_processes: int | None,
    mixed_precision: str,
    dynamo_backend: str,
) -> None:
    # Get path to the training script
    script_dir = Path(__file__).parent
    training_script = str(script_dir / "train.py")

    if num_processes is None:
        # Get number of available GPUs from nvidia-smi
        try:
            gpu_list = subprocess.check_output(["nvidia-smi", "-L"], encoding="utf-8")
            num_processes = len(gpu_list.split("\n")) - 1
            logger.debug(f"Found {num_processes} GPUs:\n{gpu_list}")
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            logger.error(f"Failed to get GPU count from nvidia-smi: {e}")
            logger.error("Falling back to 1 GPU")
            num_processes = 1

    # Convert args to form that can be passed to the training script
    training_args = [config]

    # Get the accelerate launch parser
    launch_parser = launch_command_parser()

    # Construct the launch arguments
    launch_args = [
        "--num_processes",
        str(num_processes),
        "--mixed_precision",
        mixed_precision,
        "--dynamo_backend",
        dynamo_backend,
        training_script,
        *training_args,
    ]

    if num_processes > 1:
        launch_args.insert(0, "--multi_gpu")

    # Parse the launch arguments
    launch_args = launch_parser.parse_args(launch_args)

    # Launch with accelerate
    launch_command(launch_args)


if __name__ == "__main__":
    main()

---
type: "always_apply"
---

# Instructions

During your interaction with the user, if you find anything reusable in this project (e.g. version of a library, model name), especially about a fix to a mistake you made or a correction you received, you should take note in the `Lessons` section in the `.augment/rules/task.md` file so you will not make the same mistake again. 

You should also use the `.augment/rules/task.md` file as a Scratchpad to organize your thoughts. Especially when you receive a new task, you should first review the content of the Scratchpad, clear old different task if necessary, first explain the task, and plan the steps you need to take to complete the task. You can use todo markers to indicate the progress, e.g.
[X] Task 1
[ ] Task 2

Also update the progress of the task in the Scratchpad when you finish a subtask.
Especially when you finished a milestone, it will help to improve your depth of task accomplishment to use the Scratchpad to reflect and plan.
The goal is to help you maintain a big picture as well as the progress of the task. Always refer to the Scratchpad when you plan the next step.

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- After writing the test code and ensuring all functions are correct, you should delete the test code files to avoid cluttering the project. Meanwhile, all test files should start with "Test_".
- No matter whether the user inputs in Chinese or English, you should default to replying in Chinese, unless the user requests you to reply in English.
- Please note that it is currently July 2025.

## augment learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- When modifying model loading code to support local paths, always implement fallback mechanisms to HuggingFace repos for compatibility
- For deep learning training scripts, prioritize memory optimization (gradient checkpointing, mixed precision, 8bit loading) especially for large models
- When creating training configurations for multi-GPU setups, consider effective batch size = batch_size × gradient_accumulation_steps × num_gpus
- Always provide comprehensive environment checking and troubleshooting guides for complex ML training setups
- For distributed training on multiple GPUs, use proper process management and error handling to avoid port conflicts and resource issues
- **路径处理bug修复**：当JSON数据集文件位于子目录中时，路径构建逻辑需要考虑data_root的正确设置，避免目录名重复。检查JSON文件位置并相应调整data_root到项目根目录
- **训练脚本参数错误修复**：train.py只接受配置文件路径，不支持额外的命令行参数如--disable_progress_bars。需要通过train_distributed.py传递accelerate参数
- **数据集文件匹配问题**：预处理过程中latents和conditions文件可能不匹配，需要确保两者数量和文件名完全对应。使用完整数据集重新生成conditions文件可以解决此问题
- **分布式训练梯度检查点错误修复**：在分布式训练中，模型被accelerator.prepare()包装为DistributedDataParallel后，无法直接调用原始模型的方法如enable_gradient_checkpointing()。需要使用accelerator.unwrap_model()获取原始模型再调用相关方法

# Scratchpad

## 当前任务：修复分布式训练中的梯度检查点错误

### 任务描述
用户在运行 `train_ltxv_2b_full.sh` 脚本时遇到分布式训练错误：
- 错误：`'DistributedDataParallel' object has no attribute 'enable_gradient_checkpointing'`
- 位置：`src/ltxv_trainer/trainer.py` 第519行
- 问题：在分布式训练中，模型被包装为DDP后无法调用梯度检查点方法

### 计划步骤
[X] 1. 查看项目README.md了解项目结构
[X] 2. 检查 `src/ltxv_trainer/trainer.py` 第519行附近的代码
[X] 3. 分析DDP包装后的模型访问问题
[X] 4. 提供修复方案，确保在DDP环境中正确访问原始模型的方法
[X] 5. 验证修复不会影响单GPU训练

### 问题分析
**根本原因**：
在`_prepare_models_for_training`方法中，第519行直接调用`self._transformer.enable_gradient_checkpointing()`。
但在分布式训练环境中，`self._transformer`已经被`accelerator.prepare()`包装为`DistributedDataParallel`对象，
而DDP对象没有`enable_gradient_checkpointing`方法，该方法只存在于原始的`LTXVideoTransformer3DModel`中。

**解决方案**：
需要使用`self._accelerator.unwrap_model()`来获取原始模型，然后调用梯度检查点方法。
这与代码中其他地方的做法一致（如第489行和第679行等）。

### 修复实施
**修改文件**: `src/ltxv_trainer/trainer.py` 第517-521行

**修改前**:
```python
# Enable gradient checkpointing if requested
if self._config.optimization.enable_gradient_checkpointing:
    self._transformer.enable_gradient_checkpointing()
```

**修改后**:
```python
# Enable gradient checkpointing if requested
if self._config.optimization.enable_gradient_checkpointing:
    # Use unwrap_model to access the original model when in distributed training
    unwrapped_transformer = self._accelerator.unwrap_model(self._transformer)
    unwrapped_transformer.enable_gradient_checkpointing()
```

**修复说明**:
1. 在分布式训练中，`accelerator.prepare()`会将模型包装为`DistributedDataParallel`
2. DDP对象不具有原始模型的所有方法，包括`enable_gradient_checkpointing()`
3. 使用`accelerator.unwrap_model()`可以获取原始的`LTXVideoTransformer3DModel`对象
4. 这种做法与项目中其他地方的模式一致，确保兼容性
5. 修复后既支持分布式训练，也不影响单GPU训练

### 下一步：运行训练并检查其他错误
[X] 1. 运行 train_ltxv_2b_full.sh 脚本
[X] 2. 监控训练启动过程，识别任何新的错误
[X] 3. 逐一解决发现的问题
[X] 4. 确保训练能够正常进行

### 发现的问题：
1. **依赖缺失**：缺少click模块 ✅ 已解决
2. **环境问题**：用户要求使用conda环境而不是uv虚拟环境 ✅ 已解决
3. **新错误**：验证阶段调度器参数错误
   - 错误：`ValueError: 'mu' must be passed when 'use_dynamic_shifting' is set to be 'True'`
   - 位置：`diffusers/schedulers/scheduling_flow_match_euler_discrete.py`

### 训练启动成功的部分：
✅ 分布式训练正确启动（8个进程）
✅ 模型加载成功（1.9B参数）
✅ 数据集加载成功（3,657个样本）
✅ 梯度检查点修复生效（没有DDP错误）

### 当前问题分析：
训练本身启动正常，但在验证阶段（step 0）生成样本视频时出错。
问题出现在调度器的动态时间步设置上，需要传递`mu`参数。

### 修复调度器mu参数问题：
**问题**：在验证阶段，FlowMatchEulerDiscreteScheduler需要mu参数进行动态时间步调整
**修复**：在`ltxv_pipeline.py`中的`retrieve_timesteps`调用前计算并传递mu参数
- 使用`calculate_shift(image_seq_len)`计算mu值
- 将mu参数传递给`retrieve_timesteps`函数

### 🎉 训练成功启动！

**所有错误已修复**：
1. ✅ **分布式训练梯度检查点错误** - 使用`accelerator.unwrap_model()`修复
2. ✅ **调度器mu参数错误** - 在验证阶段正确计算并传递mu参数
3. ✅ **依赖缺失问题** - 使用conda环境和pip安装

**训练状态确认**：
- ✅ 分布式训练正确启动（8个进程）
- ✅ 模型加载成功（1.9B参数）
- ✅ 数据集加载成功（3,657个样本）
- ✅ 验证视频生成成功（3/3完成）
- ✅ 训练循环开始（Loss: 0.7581）

**内存问题**：
- ⚠️ 在优化器初始化阶段出现CUDA内存不足
- 这是预期的，2B模型全量训练需要大量GPU内存
- 建议：减少batch_size或使用更多内存优化技术

### 📝 LoRA微调配置已创建

**创建的文件**：
1. `configs/ltxv_2b_lora_custom.yaml` - LoRA训练配置文件
2. `train_ltxv_2b_lora.sh` - LoRA训练启动脚本

**LoRA配置特点**：
- 训练模式：LoRA（低秩适应）
- LoRA rank: 64（平衡质量和效率）
- 更大的batch_size: 2（LoRA内存需求更少）
- 更高的学习率: 1e-4（LoRA典型设置）
- Linear调度器：稳定的学习率衰减
- 目标模块：attention和projection层

### 🎉 LoRA训练成功启动！

**训练状态确认**：
- ✅ LoRA适配器成功添加（rank 64）
- ✅ 可训练参数：58,998,784（相比全量训练的1.9B大大减少）
- ✅ 分布式训练正常运行（8个进程）
- ✅ 验证视频生成成功（4个样本）
- ✅ 训练循环正常进行（Loss在0.2-0.6之间波动）
- ✅ 内存使用正常（没有OOM错误）
- ✅ 预计训练时间：约4.5小时完成3000步

**性能指标**：
- 每步训练时间：约5.1秒
- 全局batch size：16（2 × 4 × 8）
- 学习率：1e-4，线性衰减
- 验证间隔：300步

# LTX-Video 2B 全量训练问题排查指南

本指南针对使用本地模型进行LTX-Video 2B全量训练时可能遇到的常见问题提供解决方案。

## 🚀 快速开始

### 1. 环境检查
```bash
# 检查环境是否就绪
python check_environment.py

# 检查特定组件
./train_ltxv_2b_full.sh check
```

### 2. 数据准备
```bash
# 仅准备数据集
./train_ltxv_2b_full.sh prepare

# 手动转换数据格式
python prepare_dataset.py train_0526_full.csv airscape --output airscape/dataset.json
```

### 3. 启动训练
```bash
# 完整训练流程
./train_ltxv_2b_full.sh

# 仅启动训练（跳过环境检查和数据准备）
./train_ltxv_2b_full.sh train
```

## ❌ 常见问题及解决方案

### 问题1: HuggingFace连接超时或下载失败

**症状:**
```
ConnectionError: HTTPSConnectionPool(host='huggingface.co', port=443)
```

**解决方案:**
1. 确认已修改 `src/ltxv_trainer/model_loader.py` 以支持本地加载
2. 检查本地模型文件是否完整：
```bash
ls -la /data1/wzy/LTXV_ALL/LTXV_models/LTX-Video/
```
3. 如果仍有问题，设置离线模式：
```bash
export HF_HUB_OFFLINE=1
export TRANSFORMERS_OFFLINE=1
```

### 问题2: CUDA内存不足 (OOM)

**症状:**
```
RuntimeError: CUDA out of memory
```

**解决方案:**
1. 减少批次大小，修改配置文件：
```yaml
optimization:
  batch_size: 1  # 从2减少到1
  gradient_accumulation_steps: 4  # 增加梯度累积
```

2. 启用梯度检查点：
```yaml
optimization:
  enable_gradient_checkpointing: true
```

3. 使用8bit文本编码器：
```yaml
acceleration:
  load_text_encoder_in_8bit: true
```

4. 设置CUDA内存分配策略：
```bash
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

### 问题3: 模型加载失败

**症状:**
```
ValueError: Invalid model source: LTXV_2B_0.9.6_DEV
```

**解决方案:**
1. 检查模型文件是否存在：
```bash
ls -la /data1/wzy/LTXV_ALL/LTXV_models/LTX-Video/ltxv-2b-0.9.6-dev-04-25.safetensors
```

2. 确认配置文件中的模型路径正确：
```yaml
model:
  model_source: "/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video"
```

### 问题4: 数据预处理失败

**症状:**
```
FileNotFoundError: Video file not found
```

**解决方案:**
1. 检查视频文件路径：
```bash
ls -la /data1/wzy/LTXV_ALL/LTX-Video-Trainer/airscape/ | head -10
```

2. 验证CSV文件格式：
```bash
head -5 /data1/wzy/LTXV_ALL/LTX-Video-Trainer/train_0526_full.csv
```

3. 重新运行数据准备：
```bash
python prepare_dataset.py train_0526_full.csv airscape --output airscape/dataset.json
```

### 问题5: 分布式训练启动失败

**症状:**
```
RuntimeError: Address already in use
```

**解决方案:**
1. 检查端口占用：
```bash
netstat -tulpn | grep :29500
```

2. 杀死相关进程：
```bash
pkill -f "train_distributed.py"
```

3. 重新启动训练：
```bash
./train_ltxv_2b_full.sh train
```

### 问题6: 权限问题

**症状:**
```
PermissionError: [Errno 13] Permission denied
```

**解决方案:**
1. 检查文件权限：
```bash
ls -la /data1/wzy/LTXV_ALL/
```

2. 修改权限：
```bash
chmod +x train_ltxv_2b_full.sh
chmod +x prepare_dataset.py
chmod +x check_environment.py
```

3. 创建输出目录：
```bash
mkdir -p /data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape
```

## 🔧 性能优化建议

### 1. A800 GPU优化设置
```yaml
optimization:
  batch_size: 2  # A800显存充足，可适当增加
  gradient_accumulation_steps: 2
  enable_gradient_checkpointing: true

acceleration:
  mixed_precision_mode: "bf16"  # A800支持bf16
  compile_with_inductor: true
```

### 2. 数据加载优化
```yaml
data:
  num_dataloader_workers: 4  # 根据CPU核心数调整
```

### 3. 检查点策略
```yaml
checkpoints:
  interval: 200  # 更频繁保存，防止训练中断
  keep_last_n: 5  # 只保留最近5个检查点节省空间
```

## 📊 监控训练进度

### 1. 查看GPU使用情况
```bash
watch -n 1 nvidia-smi
```

### 2. 监控训练日志
```bash
tail -f /data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape/training.log
```

### 3. 检查验证结果
```bash
ls -la /data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape/validation_samples/
```

## 🆘 获取帮助

如果遇到其他问题：

1. **检查日志文件**: 查看详细的错误信息
2. **运行环境检查**: `python check_environment.py`
3. **查看官方文档**: `docs/troubleshooting.md`
4. **社区支持**: 加入Discord社区获取帮助

## 📝 训练完成后

### 1. 验证模型
```bash
ls -la /data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape/
```

### 2. 转换为ComfyUI格式（如需要）
```bash
python scripts/convert_checkpoint.py \
  /data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape/model_weights.safetensors \
  --output-path /data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape/model_weights_comfy.safetensors
```

### 3. 测试推理
使用训练好的模型进行推理测试，确保模型正常工作。

#!/usr/bin/env python3
"""
测试预处理的第一步 - 加载模型组件
"""

import os
# 必须在任何其他导入之前设置
os.environ["HF_HUB_OFFLINE"] = "1"
os.environ["TRANSFORMERS_OFFLINE"] = "1"

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_caption_processing():
    """测试caption处理步骤"""
    print("开始测试caption处理...")
    
    # 导入必要的模块
    from ltxv_trainer.model_loader import load_text_encoder, load_tokenizer
    
    try:
        print("加载tokenizer...")
        tokenizer = load_tokenizer()
        print("✅ tokenizer加载成功")
        
        print("加载text_encoder...")
        text_encoder = load_text_encoder(load_in_8bit=True)
        print("✅ text_encoder加载成功")
        
        # 测试编码一个简单的文本
        print("测试文本编码...")
        test_text = "The video is egocentric/first-person perspective, captured from a camera mounted on a drone."
        
        # 简单的编码测试
        inputs = tokenizer(test_text, return_tensors="pt", padding=True, truncation=True, max_length=512)
        print("✅ 文本编码成功")
        
        return True
        
    except Exception as e:
        print(f"❌ caption处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("测试预处理步骤...")
    print(f"HF_HUB_OFFLINE: {os.environ.get('HF_HUB_OFFLINE')}")
    print(f"TRANSFORMERS_OFFLINE: {os.environ.get('TRANSFORMERS_OFFLINE')}")
    print("-" * 50)
    
    success = test_caption_processing()
    
    print("-" * 50)
    if success:
        print("✅ 预处理步骤测试成功！")
        return 0
    else:
        print("❌ 预处理步骤测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())

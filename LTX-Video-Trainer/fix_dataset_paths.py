#!/usr/bin/env python3
"""
修复数据集路径问题 - 将绝对路径转换为相对路径
"""

import json
from pathlib import Path

def fix_dataset_paths():
    """修复数据集中的路径问题"""
    
    # 文件路径
    input_file = "/data1/wzy/LTXV_ALL/LTX-Video-Trainer/airscape/dataset_remaining_videos.json"
    output_file = "/data1/wzy/LTXV_ALL/LTX-Video-Trainer/airscape/dataset_remaining_videos_fixed.json"
    
    # 基础路径
    base_path = Path("/data1/wzy/LTXV_ALL/LTX-Video-Trainer")
    
    print("修复数据集路径...")
    
    # 读取原始数据集
    with open(input_file, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    print(f"原始数据集条目数: {len(dataset)}")
    
    # 修复路径
    fixed_dataset = []
    for item in dataset:
        fixed_item = item.copy()
        
        # 将绝对路径转换为相对路径
        abs_path = Path(item['media_path'])
        try:
            rel_path = abs_path.relative_to(base_path)
            fixed_item['media_path'] = str(rel_path)
        except ValueError:
            # 如果无法转换为相对路径，保持原样
            print(f"警告: 无法转换路径 {abs_path}")
            fixed_item['media_path'] = item['media_path']
        
        fixed_dataset.append(fixed_item)
    
    # 保存修复后的数据集
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(fixed_dataset, f, ensure_ascii=False, indent=2)
    
    print(f"修复后的数据集已保存到: {output_file}")
    print(f"修复后数据集条目数: {len(fixed_dataset)}")
    
    # 显示前几个条目的路径
    print("\n前3个条目的路径:")
    for i, item in enumerate(fixed_dataset[:3]):
        print(f"  {i+1}. {item['media_path']}")
    
    return output_file

if __name__ == "__main__":
    fix_dataset_paths()

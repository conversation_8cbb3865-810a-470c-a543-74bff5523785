#!/bin/bash

# LTX-Video 2B LoRA Training Script
# This script runs LoRA fine-tuning on the LTX-Video 2B model using the airscape dataset

set -e  # Exit on any error

# Configuration
CONFIG_FILE="configs/ltxv_2b_lora_custom.yaml"
NUM_GPUS=8
MIXED_PRECISION="bf16"
DYNAMO_BACKEND="no"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  LTX-Video 2B LoRA Training Script${NC}"
echo -e "${BLUE}========================================${NC}"

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}Error: Configuration file $CONFIG_FILE not found!${NC}"
    exit 1
fi

# Check if preprocessed data exists
DATA_DIR="airscape/.precomputed"
if [ ! -d "$DATA_DIR" ]; then
    echo -e "${RED}Error: Preprocessed data directory $DATA_DIR not found!${NC}"
    echo -e "${YELLOW}Please run data preprocessing first.${NC}"
    exit 1
fi

# Check data files
LATENTS_COUNT=$(find "$DATA_DIR/latents" -name "*.pt" 2>/dev/null | wc -l)
CONDITIONS_COUNT=$(find "$DATA_DIR/conditions" -name "*.pt" 2>/dev/null | wc -l)

echo -e "${GREEN}Data Check:${NC}"
echo -e "  Latents files: $LATENTS_COUNT"
echo -e "  Conditions files: $CONDITIONS_COUNT"

if [ "$LATENTS_COUNT" -eq 0 ] || [ "$CONDITIONS_COUNT" -eq 0 ]; then
    echo -e "${RED}Error: No preprocessed data files found!${NC}"
    exit 1
fi

if [ "$LATENTS_COUNT" -ne "$CONDITIONS_COUNT" ]; then
    echo -e "${YELLOW}Warning: Latents and conditions file counts don't match!${NC}"
fi

# Display training configuration
echo -e "${GREEN}Training Configuration:${NC}"
echo -e "  Config file: $CONFIG_FILE"
echo -e "  Number of GPUs: $NUM_GPUS"
echo -e "  Mixed precision: $MIXED_PRECISION"
echo -e "  Training mode: LoRA"
echo -e "  Data samples: $LATENTS_COUNT"

# Create output directory
OUTPUT_DIR="/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape"
mkdir -p "$OUTPUT_DIR"
echo -e "  Output directory: $OUTPUT_DIR"

echo -e "${BLUE}========================================${NC}"

# Check GPU availability
echo -e "${GREEN}GPU Check:${NC}"
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=index,name,memory.total,memory.free --format=csv,noheader,nounits | head -8
else
    echo -e "${YELLOW}nvidia-smi not available, skipping GPU check${NC}"
fi

echo -e "${BLUE}========================================${NC}"

# Start training
echo -e "${GREEN}Starting LoRA training...${NC}"
echo -e "${YELLOW}Note: LoRA training uses much less memory than full fine-tuning${NC}"

# Run the distributed training
python scripts/train_distributed.py \
    "$CONFIG_FILE" \
    --num_processes "$NUM_GPUS" \
    --mixed_precision "$MIXED_PRECISION" \
    --dynamo_backend "$DYNAMO_BACKEND"

# Check if training completed successfully
if [ $? -eq 0 ]; then
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}  LoRA Training Completed Successfully!${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo -e "Output directory: $OUTPUT_DIR"
    echo -e "Check the output directory for:"
    echo -e "  - Model checkpoints (*.safetensors)"
    echo -e "  - Training logs"
    echo -e "  - Validation videos"
else
    echo -e "${RED}========================================${NC}"
    echo -e "${RED}  Training Failed!${NC}"
    echo -e "${RED}========================================${NC}"
    echo -e "Check the error messages above for details."
    exit 1
fi

#!/bin/bash

# LTX-Video 2B 全量训练启动脚本
# 针对8张A800 GPU优化的训练脚本

# set -e  # 遇到错误立即退出

# 配置参数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="/data1/wzy/LTXV_ALL/LTX-Video-Trainer"
MODEL_PATH="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video"
DATA_DIR="/data1/wzy/LTXV_ALL/LTX-Video-Trainer/airscape"
CSV_FILE="/data1/wzy/LTXV_ALL/LTX-Video-Trainer/train_0526_full.csv"
OUTPUT_DIR="/data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape"
CONFIG_FILE="configs/ltxv_2b_full_custom.yaml"
CONDA_ENV="LTX-Video-Trainer"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    print_status "检查训练环境..."
    
    # 检查CUDA
    if ! command -v nvidia-smi &> /dev/null; then
        print_error "未找到nvidia-smi，请确保CUDA已正确安装"
        exit 1
    fi
    
    # 检查GPU数量
    GPU_COUNT=$(nvidia-smi -L | wc -l)
    print_status "检测到 ${GPU_COUNT} 张GPU"
    
    if [ "$GPU_COUNT" -ne 8 ]; then
        print_warning "期望8张GPU，但检测到${GPU_COUNT}张GPU"
    fi
    
    # 检查conda环境
    if ! conda info --envs | grep -q "$CONDA_ENV"; then
        print_error "未找到conda环境: $CONDA_ENV"
        print_status "请先创建并激活conda环境"
        exit 1
    fi
    
    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        print_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    # 检查模型文件
    if [ ! -d "$MODEL_PATH" ]; then
        print_error "模型目录不存在: $MODEL_PATH"
        exit 1
    fi
    
    # 检查数据文件
    if [ ! -f "$CSV_FILE" ]; then
        print_error "CSV文件不存在: $CSV_FILE"
        exit 1
    fi
    
    if [ ! -d "$DATA_DIR" ]; then
        print_error "数据目录不存在: $DATA_DIR"
        exit 1
    fi
    
    print_success "环境检查通过"
}

# 准备数据集
prepare_dataset() {
    print_status "准备数据集..."
    
    cd "$PROJECT_ROOT"
    
    # 激活conda环境
    source "$(conda info --base)/etc/profile.d/conda.sh"
    conda activate "$CONDA_ENV"
    
    # 检查是否已经预处理过
    PRECOMPUTED_DIR="$DATA_DIR/.precomputed"
    if [ -d "$PRECOMPUTED_DIR" ] && [ "$(ls -A $PRECOMPUTED_DIR)" ]; then
        print_warning "检测到已存在预处理数据，跳过数据预处理"
        print_status "如需重新预处理，请删除目录: $PRECOMPUTED_DIR"
        return 0
    fi
    
    # 运行数据准备脚本
    print_status "转换CSV格式并预处理数据..."
    python prepare_dataset.py "$CSV_FILE" "$DATA_DIR" \
        --output "$DATA_DIR/dataset.json" \
        --resolution-buckets "768x448x89" \
        --model-source "LTXV_2B_0.9.6_DEV"
    
    print_success "数据集准备完成"
}

# 启动训练
start_training() {
    print_status "启动训练..."
    
    cd "$PROJECT_ROOT"
    
    # 激活conda环境
    source "$(conda info --base)/etc/profile.d/conda.sh"
    conda activate "$CONDA_ENV"
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    # 设置环境变量
    export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
    export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    
    # 启动分布式训练
    print_status "使用8张GPU启动分布式训练..."
    print_status "配置文件: $CONFIG_FILE"
    print_status "输出目录: $OUTPUT_DIR"
    
    python scripts/train_distributed.py "$CONFIG_FILE" \
        --num_processes 8 \
        --mixed_precision bf16 \
        --dynamo_backend no
    
    print_success "训练完成!"
}

# 主函数
main() {
    print_status "开始LTX-Video 2B全量训练"
    print_status "项目目录: $PROJECT_ROOT"
    print_status "模型路径: $MODEL_PATH"
    print_status "数据目录: $DATA_DIR"
    print_status "输出目录: $OUTPUT_DIR"
    
    check_environment
    # prepare_dataset
    start_training
    
    print_success "训练流程完成!"
    print_status "训练结果保存在: $OUTPUT_DIR"
}

# 处理命令行参数
case "${1:-}" in
    "check")
        check_environment
        ;;
    "prepare")
        prepare_dataset
        ;;
    "train")
        start_training
        ;;
    "")
        main
        ;;
    *)
        echo "用法: $0 [check|prepare|train]"
        echo "  check   - 仅检查环境"
        echo "  prepare - 仅准备数据集"
        echo "  train   - 仅启动训练"
        echo "  (无参数) - 执行完整流程"
        exit 1
        ;;
esac

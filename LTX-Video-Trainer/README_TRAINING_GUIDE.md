# LTX-Video 2B 全量训练完整指南

本指南提供了在您的环境中使用本地模型进行LTX-Video 2B全量训练的完整解决方案。

## 📋 环境配置

### 硬件要求
- **GPU**: 8张A800 GPU (推荐)
- **内存**: 至少64GB系统内存
- **存储**: 至少500GB可用空间

### 软件环境
- **操作系统**: Linux
- **Python**: 3.8+
- **CUDA**: 11.8+
- **Conda环境**: LTX-Video-Trainer

## 🚀 快速开始

### 1. 环境检查
首先运行环境检查脚本确保所有依赖都已正确安装：

```bash
cd /data1/wzy/LTXV_ALL/LTX-Video-Trainer
python check_environment.py
```

如果检查通过，您将看到所有组件的状态报告。

### 2. 一键启动训练
使用提供的自动化脚本启动完整的训练流程：

```bash
./train_ltxv_2b_full.sh
```

这个脚本会自动执行：
- 环境检查
- 数据格式转换和预处理
- 8卡分布式训练启动

### 3. 分步执行（可选）
如果需要分步控制，可以单独执行各个步骤：

```bash
# 仅检查环境
./train_ltxv_2b_full.sh check

# 仅准备数据集
./train_ltxv_2b_full.sh prepare

# 仅启动训练
./train_ltxv_2b_full.sh train
```

## 📁 文件结构

```
LTX-Video-Trainer/
├── configs/
│   └── ltxv_2b_full_custom.yaml      # 自定义训练配置
├── src/ltxv_trainer/
│   └── model_loader.py               # 修改后的模型加载器（支持本地加载）
├── prepare_dataset.py                # 数据预处理脚本
├── train_ltxv_2b_full.sh            # 训练启动脚本
├── check_environment.py             # 环境检查脚本
├── TROUBLESHOOTING_CN.md            # 问题排查指南
└── README_TRAINING_GUIDE.md         # 本文档
```

## ⚙️ 配置说明

### 训练配置 (ltxv_2b_full_custom.yaml)

**模型配置**:
```yaml
model:
  model_source: "/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video"  # 本地模型路径
  training_mode: "full"  # 全量训练
```

**优化参数** (针对A800优化):
```yaml
optimization:
  learning_rate: 8e-6           # 稳定的学习率
  steps: 2000                   # 训练步数
  batch_size: 1                 # 每GPU批次大小
  gradient_accumulation_steps: 8 # 梯度累积
  enable_gradient_checkpointing: true  # 节省显存
```

**加速设置**:
```yaml
acceleration:
  mixed_precision_mode: "bf16"  # A800支持bf16
  load_text_encoder_in_8bit: true  # 8bit文本编码器
```

## 🔧 关键修改说明

### 1. 本地模型加载
修改了 `src/ltxv_trainer/model_loader.py`，实现：
- 优先从本地路径加载模型组件
- 失败时自动回退到HuggingFace下载
- 支持离线训练，避免网络连接问题

### 2. 数据格式转换
`prepare_dataset.py` 脚本功能：
- 将CSV格式转换为训练器期望的JSON格式
- 自动检查视频文件存在性
- 运行数据预处理和缓存

### 3. 分布式训练优化
- 使用8张GPU并行训练
- 优化的批次大小和梯度累积设置
- 内存使用优化

## 📊 训练监控

### 查看训练进度
```bash
# 监控GPU使用情况
watch -n 1 nvidia-smi

# 查看训练输出目录
ls -la /data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape/

# 检查验证样本
ls -la /data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape/validation_samples/
```

### 训练输出
训练完成后，您将在输出目录中找到：
- `model_weights.safetensors` - 训练好的模型权重
- `training_config.yaml` - 训练配置副本
- `validation_samples/` - 验证视频样本
- 检查点文件（如果启用）

## ❗ 常见问题

### 1. HuggingFace连接问题
如果遇到网络连接问题，设置离线模式：
```bash
export HF_HUB_OFFLINE=1
export TRANSFORMERS_OFFLINE=1
```

### 2. 显存不足
调整配置文件中的批次大小：
```yaml
optimization:
  batch_size: 1  # 减少批次大小
  gradient_accumulation_steps: 8  # 增加梯度累积
```

### 3. 数据路径问题
确保所有路径都正确：
- 模型路径: `/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video`
- 数据路径: `/data1/wzy/LTXV_ALL/LTX-Video-Trainer/airscape`
- 输出路径: `/data1/wzy/LTXV_ALL/outputs/ltxv_2b_full_airscape`

详细的问题排查请参考 `TROUBLESHOOTING_CN.md`。

## 🎯 性能优化建议

1. **使用SSD存储**: 将数据和输出目录放在SSD上
2. **调整工作进程**: 根据CPU核心数调整 `num_dataloader_workers`
3. **监控显存使用**: 使用 `nvidia-smi` 监控显存使用情况
4. **定期保存检查点**: 配置合适的检查点间隔

## 📞 获取帮助

如果遇到问题：
1. 首先运行 `python check_environment.py` 检查环境
2. 查看 `TROUBLESHOOTING_CN.md` 获取解决方案
3. 检查训练日志获取详细错误信息

## 🎉 训练完成后

训练完成后，您可以：
1. 使用训练好的模型进行推理测试
2. 转换模型格式（如ComfyUI格式）
3. 评估模型性能和质量

祝您训练顺利！🚀

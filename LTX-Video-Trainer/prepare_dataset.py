#!/usr/bin/env python3
"""
数据集准备脚本 - 为LTX-Video训练准备数据集
将现有的CSV格式转换为训练器期望的格式，并进行数据预处理
"""

import pandas as pd
import json
import os
from pathlib import Path
import argparse
import subprocess
import sys

def convert_csv_to_dataset_format(csv_path, video_dir, output_path):
    """
    将CSV文件转换为训练器期望的数据集格式
    
    Args:
        csv_path: 原始CSV文件路径
        video_dir: 视频文件目录
        output_path: 输出的数据集文件路径
    """
    print(f"读取CSV文件: {csv_path}")
    df = pd.read_csv(csv_path)
    
    # 检查必要的列
    if 'updated_video_id' not in df.columns or 'description' not in df.columns:
        raise ValueError("CSV文件必须包含 'updated_video_id' 和 'description' 列")
    
    dataset = []
    video_dir_path = Path(video_dir)
    missing_videos = []
    
    for _, row in df.iterrows():
        video_filename = row['updated_video_id']
        caption = row['description']
        
        # 构建完整的视频路径
        video_path = video_dir_path / video_filename
        
        # 检查视频文件是否存在
        if not video_path.exists():
            missing_videos.append(str(video_path))
            continue
            
        dataset.append({
            "caption": caption,
            "media_path": str(video_path)
        })
    
    if missing_videos:
        print(f"警告: 找不到以下 {len(missing_videos)} 个视频文件:")
        for video in missing_videos[:10]:  # 只显示前10个
            print(f"  - {video}")
        if len(missing_videos) > 10:
            print(f"  ... 还有 {len(missing_videos) - 10} 个文件")
    
    print(f"成功处理 {len(dataset)} 个视频文件")
    
    # 保存为JSON格式
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)
    
    print(f"数据集文件已保存到: {output_path}")
    return output_path

def run_preprocessing(dataset_path, resolution_buckets="768x448x89", model_source="LTXV_2B_0.9.6_DEV"):
    """
    运行数据预处理
    
    Args:
        dataset_path: 数据集JSON文件路径
        resolution_buckets: 分辨率桶设置
        model_source: 模型源
    """
    print(f"开始预处理数据集: {dataset_path}")
    print(f"分辨率设置: {resolution_buckets}")
    print(f"模型源: {model_source}")
    
    cmd = [
        sys.executable, "scripts/preprocess_dataset.py",
        dataset_path,
        "--resolution-buckets", resolution_buckets,
        "--caption-column", "caption",
        "--video-column", "media_path",
        "--model-source", model_source
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("预处理完成!")
        print("输出:", result.stdout)
        if result.stderr:
            print("警告:", result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"预处理失败: {e}")
        print("错误输出:", e.stderr)
        print("标准输出:", e.stdout)
        raise

def main():
    parser = argparse.ArgumentParser(description="准备LTX-Video训练数据集")
    parser.add_argument("csv_path", help="原始CSV文件路径")
    parser.add_argument("video_dir", help="视频文件目录")
    parser.add_argument("--output", "-o", default="dataset.json", help="输出数据集文件路径")
    parser.add_argument("--resolution-buckets", default="768x448x89", help="分辨率桶设置")
    parser.add_argument("--model-source", default="LTXV_2B_0.9.6_DEV", help="模型源")
    parser.add_argument("--skip-preprocessing", action="store_true", help="跳过预处理步骤")
    
    args = parser.parse_args()
    
    # 转换CSV格式
    dataset_path = convert_csv_to_dataset_format(args.csv_path, args.video_dir, args.output)
    
    # 运行预处理
    if not args.skip_preprocessing:
        run_preprocessing(dataset_path, args.resolution_buckets, args.model_source)
    else:
        print("跳过预处理步骤")
        print(f"要手动运行预处理，请执行:")
        print(f"python scripts/preprocess_dataset.py {dataset_path} --resolution-buckets {args.resolution_buckets} --caption-column caption --video-column media_path --model-source {args.model_source}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
单个评估测试脚本

用于测试改进版LoRA评估脚本的单个checkpoint评估功能
"""

import os
import sys
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_single_evaluation():
    """测试单个checkpoint的评估"""
    
    # 导入改进版评估器
    from evaluate_lora_improved import ImprovedLoRAEvaluator
    
    # 创建评估器
    evaluator = ImprovedLoRAEvaluator(
        checkpoints_dir="/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints",
        base_model_path="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video",
        test_videos_dir="/data1/wzy/LTXV_ALL/demo_video/airscape",
        prompt_file="/data1/wzy/LTXV_ALL/demo_video/prompt.txt",
        output_dir="/data1/wzy/LTXV_ALL/lora_evaluation_test"
    )
    
    # 初始化
    logger.info("开始初始化...")
    if not evaluator.setup():
        logger.error("初始化失败")
        return False
    
    logger.info("初始化成功！")
    
    # 只测试第一个checkpoint和第一个视频
    if evaluator.checkpoints and evaluator.test_videos:
        first_checkpoint = evaluator.checkpoints[0]
        first_video = evaluator.test_videos[0]
        
        logger.info(f"测试checkpoint: {first_checkpoint['name']}")
        logger.info(f"测试视频: {first_video['name']}")
        
        # 执行单个评估
        try:
            success = evaluator.evaluate_single_checkpoint(first_checkpoint, first_video)
            if success:
                logger.info("✅ 单个评估测试成功！")
                return True
            else:
                logger.error("❌ 单个评估测试失败")
                return False
        except Exception as e:
            logger.error(f"评估过程中出现异常: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    else:
        logger.error("没有找到checkpoint或测试视频")
        return False

def main():
    """主函数"""
    logger.info("开始单个评估测试...")
    
    try:
        success = test_single_evaluation()
        if success:
            logger.info("🎉 测试完成！单个评估功能正常工作")
        else:
            logger.error("💥 测试失败！需要进一步调试")
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()

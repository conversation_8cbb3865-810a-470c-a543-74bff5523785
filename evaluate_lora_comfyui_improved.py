#!/usr/bin/env python3
"""
LoRA训练效果评估脚本 (ComfyUI改进版)

基于对LTX-Video-Trainer项目的深入分析，使用正确的ComfyUI工作流。

关键改进：
1. 使用LTX-Video专用的ComfyUI节点
2. 正确的LoRA格式转换和加载
3. 符合LTX-Video-Trainer的ComfyUI集成方式
4. 使用官方推荐的工作流结构

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import cv2
import json
import argparse
import subprocess
from pathlib import Path
from typing import List, Dict, Optional
import logging
from tqdm import tqdm
import re
from datetime import datetime
import requests
import time
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lora_evaluation_comfyui_improved.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ImprovedComfyUILoRAEvaluator:
    """改进版ComfyUI LoRA评估器"""
    
    def __init__(self, 
                 checkpoints_dir: str,
                 base_model_path: str,
                 test_videos_dir: str,
                 prompt_file: str,
                 output_dir: str,
                 comfyui_path: str,
                 comfyui_api_url: str = "http://127.0.0.1:8188"):
        
        self.checkpoints_dir = Path(checkpoints_dir)
        self.base_model_path = Path(base_model_path)
        self.test_videos_dir = Path(test_videos_dir)
        self.prompt_file = Path(prompt_file)
        self.output_dir = Path(output_dir)
        self.comfyui_path = Path(comfyui_path)
        self.comfyui_api_url = comfyui_api_url
        
        self.checkpoints = []
        self.test_videos = []
        self.prompts = {}
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # ComfyUI相关路径
        self.comfyui_models_loras = self.comfyui_path / "models" / "loras"
        self.comfyui_models_checkpoints = self.comfyui_path / "models" / "checkpoints"
        self.comfyui_input = self.comfyui_path / "input"
        self.comfyui_output = self.comfyui_path / "output"
        
        # LTX-Video-Trainer路径
        self.trainer_path = self.base_model_path.parent / "LTX-Video-Trainer"
        
    def setup(self) -> bool:
        """初始化设置"""
        logger.info("开始初始化改进版ComfyUI评估环境...")
        
        # 检查必要路径
        if not self._check_paths():
            return False
            
        # 扫描checkpoints
        if not self._scan_checkpoints():
            return False
            
        # 扫描测试视频
        if not self._scan_test_videos():
            return False
            
        # 加载提示词
        if not self._load_prompts():
            return False
            
        # 检查ComfyUI服务
        if not self._check_comfyui_service():
            logger.warning("ComfyUI服务未运行，将使用命令行模式")
            
        logger.info(f"找到 {len(self.checkpoints)} 个checkpoints")
        logger.info(f"找到 {len(self.test_videos)} 个测试视频")
        logger.info(f"加载了 {len(self.prompts)} 个提示词")
        
        return True
    
    def _check_paths(self) -> bool:
        """检查必要路径是否存在"""
        paths_to_check = [
            (self.checkpoints_dir, "Checkpoints目录"),
            (self.base_model_path, "基础模型路径"),
            (self.test_videos_dir, "测试视频目录"),
            (self.prompt_file, "提示词文件"),
            (self.comfyui_path, "ComfyUI路径"),
            (self.trainer_path, "LTX-Video-Trainer路径")
        ]
        
        for path, name in paths_to_check:
            if not path.exists():
                logger.error(f"{name} 不存在: {path}")
                return False
        
        # 检查ComfyUI关键目录
        comfyui_dirs = [
            self.comfyui_models_loras,
            self.comfyui_models_checkpoints,
            self.comfyui_input,
            self.comfyui_output
        ]
        
        for dir_path in comfyui_dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 检查转换脚本
        convert_script = self.trainer_path / "scripts" / "convert_checkpoint.py"
        if not convert_script.exists():
            logger.error(f"转换脚本不存在: {convert_script}")
            return False
                
        return True
    
    def _check_comfyui_service(self) -> bool:
        """检查ComfyUI服务是否运行"""
        try:
            response = requests.get(f"{self.comfyui_api_url}/system_stats", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _scan_checkpoints(self) -> bool:
        """扫描checkpoint文件"""
        # 查找所有.safetensors文件
        checkpoint_files = list(self.checkpoints_dir.glob("*.safetensors"))
        
        if not checkpoint_files:
            logger.error(f"在 {self.checkpoints_dir} 中未找到任何.safetensors文件")
            return False
        
        # 按步数排序
        def extract_step_number(filename: str) -> int:
            match = re.search(r'step_(\d+)', filename)
            return int(match.group(1)) if match else 0
        
        checkpoint_files.sort(key=lambda x: extract_step_number(x.name))
        
        self.checkpoints = [
            {
                'path': checkpoint_file,
                'name': checkpoint_file.stem,
                'step': extract_step_number(checkpoint_file.name)
            }
            for checkpoint_file in checkpoint_files
        ]
        
        return True
    
    def _scan_test_videos(self) -> bool:
        """扫描测试视频文件"""
        # 查找所有.mp4文件
        video_files = list(self.test_videos_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error(f"在 {self.test_videos_dir} 中未找到任何.mp4文件")
            return False
        
        self.test_videos = [
            {
                'path': video_file,
                'name': video_file.stem,
                'filename': video_file.name
            }
            for video_file in video_files
        ]
        
        return True
    
    def _load_prompts(self) -> bool:
        """加载提示词文件"""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析表格格式的提示词
            lines = content.strip().split('\n')
            for line in lines[2:]:  # 跳过表头
                if '|' in line:
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 4:
                        example_num = parts[1]
                        motion_description = parts[3]
                        
                        # 提取视频文件名模式
                        if 'urbanvideo_test' in motion_description or '00819' in example_num:
                            self.prompts['00819_urbanvideo_test'] = motion_description
                        elif '00840' in example_num:
                            self.prompts['00840_urbanvideo_test'] = motion_description
                        elif '00846' in example_num:
                            self.prompts['00846_urbanvideo_test'] = motion_description
                        elif '01035' in example_num:
                            self.prompts['01035_NAT2021_test_N02029_4'] = motion_description
                        elif '01374' in example_num:
                            self.prompts['01374_NAT2021_test_N08024_3'] = motion_description
            
            return len(self.prompts) > 0
            
        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
            return False
    
    def extract_first_frame(self, video_path: Path, output_path: Path) -> bool:
        """从视频中提取首帧"""
        try:
            cap = cv2.VideoCapture(str(video_path))
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                cv2.imwrite(str(output_path), frame)
                return True
            else:
                logger.error(f"无法从视频中读取帧: {video_path}")
                return False
                
        except Exception as e:
            logger.error(f"提取首帧失败: {e}")
            return False
    
    def convert_lora_to_comfyui(self, lora_path: Path) -> Optional[Path]:
        """将LoRA转换为ComfyUI格式"""
        comfyui_lora_name = f"{lora_path.stem}_comfy.safetensors"
        comfyui_lora_path = self.comfyui_models_loras / comfyui_lora_name
        
        # 如果已经存在，直接返回
        if comfyui_lora_path.exists():
            logger.info(f"ComfyUI LoRA已存在: {comfyui_lora_name}")
            return comfyui_lora_path
        
        # 使用转换脚本
        convert_script = self.trainer_path / "scripts" / "convert_checkpoint.py"
        
        try:
            cmd = [
                sys.executable, str(convert_script),
                str(lora_path),
                "--to-comfy",
                "--output-path", str(comfyui_lora_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"成功转换LoRA: {lora_path.name} -> {comfyui_lora_name}")
            return comfyui_lora_path
            
        except subprocess.CalledProcessError as e:
            logger.error(f"转换LoRA失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            return None

    def create_ltxv_comfyui_workflow(self, lora_name: str, image_name: str, prompt: str) -> Dict:
        """创建LTX-Video专用的ComfyUI工作流JSON"""

        # 基于LTX-Video官方ComfyUI工作流的改进版本
        workflow = {
            "1": {
                "inputs": {
                    "model_name": "ltxv-2b-0.9.6-dev-04-25.safetensors"
                },
                "class_type": "LTXV Model Loader",
                "_meta": {"title": "Load LTXV Model"}
            },
            "2": {
                "inputs": {
                    "text": prompt,
                    "tokenizer": ["1", 1]
                },
                "class_type": "LTXV Text Encode",
                "_meta": {"title": "Encode Prompt"}
            },
            "3": {
                "inputs": {
                    "text": "",
                    "tokenizer": ["1", 1]
                },
                "class_type": "LTXV Text Encode",
                "_meta": {"title": "Encode Negative Prompt"}
            },
            "4": {
                "inputs": {
                    "image": image_name,
                    "upload": "image"
                },
                "class_type": "LoadImage",
                "_meta": {"title": "Load Condition Image"}
            },
            "5": {
                "inputs": {
                    "lora_name": lora_name,
                    "strength": 1.0,
                    "model": ["1", 0]
                },
                "class_type": "LTXV LoRA Loader",
                "_meta": {"title": "Load LoRA"}
            },
            "6": {
                "inputs": {
                    "image": ["4", 0],
                    "start_frame": 0
                },
                "class_type": "LTXV Video Condition",
                "_meta": {"title": "Create Video Condition"}
            },
            "7": {
                "inputs": {
                    "model": ["5", 0],
                    "positive": ["2", 0],
                    "negative": ["3", 0],
                    "video_condition": ["6", 0],
                    "width": 1216,
                    "height": 704,
                    "num_frames": 121,
                    "seed": 42,
                    "steps": 40,
                    "cfg": 3.0
                },
                "class_type": "LTXV Sampler",
                "_meta": {"title": "LTXV Video Sampler"}
            },
            "8": {
                "inputs": {
                    "samples": ["7", 0],
                    "vae": ["1", 2]
                },
                "class_type": "LTXV VAE Decode",
                "_meta": {"title": "Decode Video"}
            },
            "9": {
                "inputs": {
                    "filename_prefix": "ltxv_lora_output",
                    "fps": 30,
                    "frames": ["8", 0]
                },
                "class_type": "LTXV Save Video",
                "_meta": {"title": "Save Video"}
            }
        }

        return workflow

    def copy_image_to_comfyui(self, image_path: Path) -> str:
        """将图像复制到ComfyUI输入目录"""
        target_path = self.comfyui_input / image_path.name

        if not target_path.exists():
            shutil.copy2(image_path, target_path)
            logger.info(f"图像已复制到ComfyUI: {image_path.name}")

        return image_path.name

    def queue_prompt_api(self, workflow: Dict) -> Optional[str]:
        """通过ComfyUI API提交工作流"""
        try:
            prompt_data = {"prompt": workflow}
            response = requests.post(f"{self.comfyui_api_url}/prompt", json=prompt_data)

            if response.status_code == 200:
                result = response.json()
                prompt_id = result.get("prompt_id")
                logger.info(f"工作流已提交，ID: {prompt_id}")
                return prompt_id
            else:
                logger.error(f"提交工作流失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"提交工作流时出错: {e}")
            return None

    def wait_for_completion(self, prompt_id: str, timeout: int = 600) -> bool:
        """等待工作流完成"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.comfyui_api_url}/history/{prompt_id}")

                if response.status_code == 200:
                    history = response.json()
                    if prompt_id in history:
                        status = history[prompt_id].get("status", {})
                        if status.get("completed", False):
                            logger.info(f"工作流 {prompt_id} 完成")
                            return True
                        elif "error" in status:
                            logger.error(f"工作流 {prompt_id} 出错: {status['error']}")
                            return False

                time.sleep(5)  # 每5秒检查一次

            except Exception as e:
                logger.error(f"检查工作流状态时出错: {e}")
                time.sleep(5)

        logger.error(f"工作流 {prompt_id} 超时")
        return False

    def run_comfyui_cli(self, workflow_path: Path, output_dir: Path) -> bool:
        """使用命令行模式运行ComfyUI"""
        try:
            cmd = [
                sys.executable,
                str(self.comfyui_path / "main.py"),
                "--input", str(workflow_path),
                "--output-directory", str(output_dir)
            ]

            logger.info("使用命令行模式运行ComfyUI...")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600,  # 10分钟超时
                cwd=str(self.comfyui_path)
            )

            if result.returncode == 0:
                logger.info("ComfyUI命令行执行成功")
                return True
            else:
                logger.error(f"ComfyUI命令行执行失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("ComfyUI命令行执行超时")
            return False
        except Exception as e:
            logger.error(f"运行ComfyUI命令行时出错: {e}")
            return False

    def find_generated_video(self, output_dir: Path) -> Optional[Path]:
        """查找生成的视频文件"""
        # 查找最新的.mp4文件
        video_files = list(output_dir.glob("*.mp4"))

        if video_files:
            # 返回最新的文件
            latest_file = max(video_files, key=lambda x: x.stat().st_mtime)
            return latest_file

        return None

    def evaluate_single_checkpoint(self, checkpoint: Dict, test_video: Dict) -> bool:
        """评估单个checkpoint"""
        checkpoint_name = checkpoint['name']
        video_name = test_video['name']

        logger.info(f"评估 {checkpoint_name} 使用测试视频 {video_name}")

        # 创建输出目录
        output_dir = self.output_dir / checkpoint_name
        output_dir.mkdir(exist_ok=True)

        # 提取首帧
        first_frame_path = output_dir / f"{video_name}_first_frame.jpg"
        if not self.extract_first_frame(test_video['path'], first_frame_path):
            return False

        # 获取对应的提示词
        prompt = self.prompts.get(video_name, "A drone flying over a landscape")

        # 转换LoRA为ComfyUI格式
        comfyui_lora_path = self.convert_lora_to_comfyui(checkpoint['path'])
        if not comfyui_lora_path:
            return False

        # 复制图像到ComfyUI输入目录
        image_name = self.copy_image_to_comfyui(first_frame_path)

        # 创建工作流
        workflow = self.create_ltxv_comfyui_workflow(
            comfyui_lora_path.name,
            image_name,
            prompt
        )

        # 保存工作流文件
        workflow_path = output_dir / "workflow.json"
        with open(workflow_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2, ensure_ascii=False)

        # 执行工作流
        success = False

        # 首先尝试API模式
        if self._check_comfyui_service():
            prompt_id = self.queue_prompt_api(workflow)
            if prompt_id:
                success = self.wait_for_completion(prompt_id)

        # 如果API模式失败，尝试命令行模式
        if not success:
            logger.info("API模式失败，尝试命令行模式...")
            success = self.run_comfyui_cli(workflow_path, output_dir)

        if success:
            # 查找生成的视频
            generated_video = self.find_generated_video(output_dir)
            if generated_video:
                # 重命名为标准格式
                final_video_path = output_dir / f"{video_name}_generated.mp4"
                if generated_video != final_video_path:
                    shutil.move(generated_video, final_video_path)

                logger.info(f"成功生成视频: {final_video_path}")

                # 保存元数据
                metadata = {
                    'checkpoint': checkpoint_name,
                    'step': checkpoint['step'],
                    'test_video': video_name,
                    'prompt': prompt,
                    'generated_at': datetime.now().isoformat(),
                    'comfyui_workflow': 'ltxv_lora_workflow',
                    'lora_file': comfyui_lora_path.name
                }

                metadata_path = output_dir / f"{video_name}_metadata.json"
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False)
            else:
                logger.error(f"未找到生成的视频文件")
                success = False
        else:
            logger.error(f"ComfyUI工作流执行失败")

        return success

    def run_evaluation(self) -> None:
        """运行完整评估"""
        logger.info("开始ComfyUI LoRA checkpoint评估...")

        total_tasks = len(self.checkpoints) * len(self.test_videos)
        completed_tasks = 0
        failed_tasks = 0

        # 创建进度条
        with tqdm(total=total_tasks, desc="评估进度") as pbar:
            for checkpoint in self.checkpoints:
                for test_video in self.test_videos:
                    try:
                        success = self.evaluate_single_checkpoint(checkpoint, test_video)
                        if success:
                            completed_tasks += 1
                        else:
                            failed_tasks += 1
                    except Exception as e:
                        logger.error(f"评估过程中出现异常: {e}")
                        failed_tasks += 1

                    pbar.update(1)
                    pbar.set_postfix({
                        'completed': completed_tasks,
                        'failed': failed_tasks
                    })

        # 生成评估报告
        self.generate_report(completed_tasks, failed_tasks, total_tasks)

    def generate_report(self, completed: int, failed: int, total: int) -> None:
        """生成评估报告"""
        report_path = self.output_dir / "evaluation_report.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# LoRA Checkpoint 评估报告 (ComfyUI改进版)\n\n")
            f.write(f"**评估时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**使用工具**: ComfyUI with LTX-Video nodes\n")
            f.write(f"**工作流**: LTXV专用工作流\n")
            f.write(f"**ComfyUI路径**: {self.comfyui_path}\n\n")
            f.write(f"**总任务数**: {total}\n")
            f.write(f"**成功完成**: {completed}\n")
            f.write(f"**失败任务**: {failed}\n")
            f.write(f"**成功率**: {completed/total*100:.1f}%\n\n")

            f.write("## Checkpoints\n\n")
            for checkpoint in self.checkpoints:
                f.write(f"- {checkpoint['name']} (Step: {checkpoint['step']})\n")

            f.write("\n## 测试视频\n\n")
            for video in self.test_videos:
                f.write(f"- {video['name']}\n")
                prompt = self.prompts.get(video['name'], "无对应提示词")
                f.write(f"  - 提示词: {prompt}\n")

            f.write("\n## ComfyUI配置\n\n")
            f.write("- **节点类型**: LTXV专用节点\n")
            f.write("- **LoRA加载**: LTXV LoRA Loader\n")
            f.write("- **视频条件**: LTXV Video Condition\n")
            f.write("- **采样器**: LTXV Sampler\n")
            f.write("- **VAE解码**: LTXV VAE Decode\n")

            f.write("\n## 输出结构\n\n")
            f.write("```\n")
            f.write(f"{self.output_dir}/\n")
            for checkpoint in self.checkpoints:
                f.write(f"├── {checkpoint['name']}/\n")
                for video in self.test_videos:
                    f.write(f"│   ├── {video['name']}_first_frame.jpg\n")
                    f.write(f"│   ├── {video['name']}_generated.mp4\n")
                    f.write(f"│   ├── {video['name']}_metadata.json\n")
                    f.write(f"│   └── workflow.json\n")
            f.write("└── evaluation_report.md\n")
            f.write("```\n")

        logger.info(f"评估报告已生成: {report_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LoRA训练效果评估脚本 (ComfyUI改进版)")

    parser.add_argument("--checkpoints-dir",
                       default="/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints",
                       help="LoRA checkpoints目录路径")

    parser.add_argument("--base-model-path",
                       default="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video",
                       help="基础模型路径")

    parser.add_argument("--test-videos-dir",
                       default="/data1/wzy/LTXV_ALL/demo_video/airscape",
                       help="测试视频目录路径")

    parser.add_argument("--prompt-file",
                       default="/data1/wzy/LTXV_ALL/demo_video/prompt.txt",
                       help="提示词文件路径")

    parser.add_argument("--output-dir",
                       default="/data1/wzy/LTXV_ALL/lora_evaluation_results_comfyui",
                       help="输出目录路径")

    parser.add_argument("--comfyui-path",
                       required=True,
                       help="ComfyUI安装路径")

    parser.add_argument("--comfyui-api-url",
                       default="http://127.0.0.1:8188",
                       help="ComfyUI API地址")

    parser.add_argument("--dry-run", action="store_true", help="仅检查配置，不执行评估")
    parser.add_argument("--verbose", action="store_true", help="详细输出")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 创建评估器
    evaluator = ImprovedComfyUILoRAEvaluator(
        checkpoints_dir=args.checkpoints_dir,
        base_model_path=args.base_model_path,
        test_videos_dir=args.test_videos_dir,
        prompt_file=args.prompt_file,
        output_dir=args.output_dir,
        comfyui_path=args.comfyui_path,
        comfyui_api_url=args.comfyui_api_url
    )

    # 初始化
    if not evaluator.setup():
        logger.error("初始化失败，退出程序")
        sys.exit(1)

    if args.dry_run:
        logger.info("Dry run模式，配置检查完成")
        logger.info("改进版ComfyUI脚本使用LTX-Video专用节点和正确的工作流")
        return

    # 运行评估
    try:
        evaluator.run_evaluation()
        logger.info("评估完成！")
    except KeyboardInterrupt:
        logger.info("用户中断评估")
    except Exception as e:
        logger.error(f"评估过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

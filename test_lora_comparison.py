#!/usr/bin/env python3
"""
LoRA权重应用效果对比测试

对比基础模型、原始脚本和修复版脚本的生成效果，
验证LoRA权重是否正确应用。

作者：AI Assistant
日期：2025-07-18
"""

import os
import sys
import logging
from pathlib import Path
import torch
import cv2
import PIL.Image
import numpy as np
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加LTX-Video-Trainer到Python路径
trainer_src_path = Path(__file__).parent / "LTX-Video-Trainer" / "src"
if trainer_src_path.exists():
    sys.path.insert(0, str(trainer_src_path))

try:
    from ltxv_trainer.ltxv_pipeline import LTXConditionPipeline, LTXVideoCondition
    from ltxv_trainer.model_loader import load_ltxv_components
    import imageio
    logger.info("✅ 成功导入所有必要模块")
except ImportError as e:
    logger.error(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class LoRAComparisonTester:
    """LoRA权重应用效果对比测试器"""
    
    def __init__(self):
        self.base_model_path = "/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video"
        self.test_video_path = "/data1/wzy/LTXV_ALL/demo_video/airscape/00819_urbanvideo_test.mp4"
        self.lora_checkpoint = "/data1/wzy/LTXV_ALL/outputs/ltxv_2b_lora_airscape/checkpoints/lora_weights_step_03000.safetensors"
        self.output_dir = Path("/data1/wzy/LTXV_ALL/lora_comparison_test")
        self.device = "cuda"
        
        # 测试提示词
        self.test_prompt = "A drone shot of a city street with cars and buildings, urban landscape, aerial view"
        
        self.pipeline = None
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("初始化LoRA对比测试器")

    def init_pipeline(self) -> bool:
        """初始化基础管道"""
        try:
            logger.info("初始化基础管道...")
            
            # 设置离线模式
            os.environ["HF_HUB_OFFLINE"] = "1"
            
            # 加载模型组件
            components = load_ltxv_components(
                model_source=str(self.base_model_path),
                load_text_encoder_in_8bit=False,
                transformer_dtype=torch.bfloat16,
                vae_dtype=torch.bfloat16,
            )
            
            # 创建管道
            self.pipeline = LTXConditionPipeline(
                vae=components.vae,
                text_encoder=components.text_encoder,
                tokenizer=components.tokenizer,
                transformer=components.transformer,
                scheduler=components.scheduler,
            )
            
            # 移动到设备
            self.pipeline = self.pipeline.to(self.device)
            
            # 启用内存优化
            if hasattr(self.pipeline, 'enable_model_cpu_offload'):
                self.pipeline.enable_model_cpu_offload()
            if hasattr(self.pipeline, 'enable_vae_tiling'):
                self.pipeline.enable_vae_tiling()
            
            logger.info("✅ 基础管道初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化管道失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def get_condition_image(self):
        """获取条件图像"""
        cap = cv2.VideoCapture(str(self.test_video_path))
        ret, frame = cap.read()
        cap.release()
        
        if not ret:
            raise ValueError(f"无法读取视频第一帧: {self.test_video_path}")
        
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        return PIL.Image.fromarray(frame_rgb)

    def generate_video(self, output_name: str, use_lora: bool = False, lora_method: str = "none") -> bool:
        """生成视频"""
        try:
            logger.info(f"生成视频: {output_name} (LoRA: {use_lora}, 方法: {lora_method})")
            
            # 获取条件图像
            condition_image = self.get_condition_image()
            
            # 创建视频条件
            video_condition = LTXVideoCondition(
                image=condition_image,
                frame_index=0,
            )
            
            # 如果使用LoRA，加载权重
            if use_lora:
                if lora_method == "original":
                    # 原始方法（有问题的方法）
                    self.pipeline.load_lora_weights(self.lora_checkpoint)
                    if hasattr(self.pipeline, 'fuse_lora'):
                        self.pipeline.fuse_lora()
                        logger.info("使用原始方法加载LoRA（fuse_lora）")
                elif lora_method == "fixed":
                    # 修复方法（正确的方法）
                    from peft import LoraConfig
                    
                    # 设置LoRA适配器
                    lora_config = LoraConfig(
                        r=32,
                        lora_alpha=32,
                        target_modules=["to_k", "to_q", "to_v", "to_out.0"],
                        lora_dropout=0.0,
                        init_lora_weights=True,
                    )
                    
                    # 添加适配器
                    self.pipeline.transformer.add_adapter(lora_config)
                    
                    # 加载权重
                    from safetensors.torch import load_file
                    state_dict = load_file(self.lora_checkpoint)
                    
                    # 调整权重键名
                    adjusted_state_dict = {}
                    for key, value in state_dict.items():
                        key = key.replace("transformer.", "")
                        if "lora_A" in key and ".default" not in key:
                            key = key.replace("lora_A", "lora_A.default")
                        elif "lora_B" in key and ".default" not in key:
                            key = key.replace("lora_B", "lora_B.default")
                        adjusted_state_dict[key] = value
                    
                    # 加载到transformer
                    self.pipeline.transformer.load_state_dict(adjusted_state_dict, strict=False)
                    self.pipeline.transformer.enable_adapters()
                    
                    logger.info("使用修复方法加载LoRA（PEFT适配器）")
            
            # 设置推理参数
            width, height = 512, 320
            num_frames = 121
            fps = 25
            generator = torch.Generator(device=self.device).manual_seed(42)
            
            # 运行推理
            logger.info("开始推理...")
            start_time = datetime.now()
            
            output = self.pipeline(
                prompt=self.test_prompt,
                conditions=[video_condition],
                width=width,
                height=height,
                num_frames=num_frames,
                generator=generator,
                guidance_scale=3.0,
                num_inference_steps=40,
            )
            
            end_time = datetime.now()
            inference_time = (end_time - start_time).total_seconds()
            
            # 保存视频
            output_path = self.output_dir / f"{output_name}.mp4"
            frames = output.frames[0]
            
            writer = imageio.get_writer(str(output_path), fps=fps)
            for frame in frames:
                if hasattr(frame, 'convert'):
                    frame_array = np.array(frame.convert('RGB'))
                else:
                    frame_array = frame
                writer.append_data(frame_array)
            writer.close()
            
            # 保存首帧
            first_frame_path = self.output_dir / f"{output_name}_first_frame.jpg"
            if frames:
                first_frame = frames[0]
                if hasattr(first_frame, 'save'):
                    first_frame.save(first_frame_path)
                else:
                    PIL.Image.fromarray(first_frame).save(first_frame_path)
            
            # 保存元数据
            metadata = {
                'name': output_name,
                'use_lora': use_lora,
                'lora_method': lora_method,
                'prompt': self.test_prompt,
                'inference_time_seconds': inference_time,
                'num_frames': len(frames),
                'generation_time': datetime.now().isoformat(),
                'parameters': {
                    'width': width,
                    'height': height,
                    'num_frames': num_frames,
                    'fps': fps,
                    'guidance_scale': 3.0,
                    'num_inference_steps': 40,
                    'seed': 42
                }
            }
            
            metadata_path = self.output_dir / f"{output_name}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 视频生成成功: {output_path}")
            logger.info(f"推理时间: {inference_time:.1f}秒")
            
            # 清理LoRA权重
            if use_lora:
                if lora_method == "original" and hasattr(self.pipeline, 'unfuse_lora'):
                    self.pipeline.unfuse_lora()
                elif lora_method == "fixed" and hasattr(self.pipeline.transformer, 'disable_adapters'):
                    self.pipeline.transformer.disable_adapters()
                    self.pipeline.transformer.delete_adapters()
            
            return True
            
        except Exception as e:
            logger.error(f"生成视频失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def run_comparison_test(self):
        """运行对比测试"""
        logger.info("开始LoRA权重应用效果对比测试...")
        
        # 初始化管道
        if not self.init_pipeline():
            logger.error("管道初始化失败")
            return
        
        # 测试1：基础模型（无LoRA）
        logger.info("=== 测试1：基础模型（无LoRA）===")
        self.generate_video("baseline_no_lora", use_lora=False)
        
        # 测试2：原始方法加载LoRA
        logger.info("=== 测试2：原始方法加载LoRA ===")
        self.generate_video("original_lora_method", use_lora=True, lora_method="original")
        
        # 测试3：修复方法加载LoRA
        logger.info("=== 测试3：修复方法加载LoRA ===")
        self.generate_video("fixed_lora_method", use_lora=True, lora_method="fixed")
        
        # 生成对比报告
        self.generate_comparison_report()
        
        logger.info("🎉 对比测试完成！")

    def generate_comparison_report(self):
        """生成对比报告"""
        report_path = self.output_dir / "comparison_report.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# LoRA权重应用效果对比测试报告\n\n")
            f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 测试配置\n")
            f.write(f"- **基础模型**: {self.base_model_path}\n")
            f.write(f"- **LoRA权重**: {self.lora_checkpoint}\n")
            f.write(f"- **测试视频**: {self.test_video_path}\n")
            f.write(f"- **测试提示词**: {self.test_prompt}\n\n")
            
            f.write("## 测试结果\n")
            f.write("### 1. 基础模型（无LoRA）\n")
            f.write("- 文件: `baseline_no_lora.mp4`\n")
            f.write("- 说明: 未应用任何LoRA权重的基础模型生成效果\n\n")
            
            f.write("### 2. 原始方法加载LoRA\n")
            f.write("- 文件: `original_lora_method.mp4`\n")
            f.write("- 方法: 使用`load_lora_weights()` + `fuse_lora()`\n")
            f.write("- 问题: 可能存在权重应用不正确的问题\n\n")
            
            f.write("### 3. 修复方法加载LoRA\n")
            f.write("- 文件: `fixed_lora_method.mp4`\n")
            f.write("- 方法: 使用PEFT适配器模式\n")
            f.write("- 优势: 正确的LoRA权重应用机制\n\n")
            
            f.write("## 分析指南\n")
            f.write("1. **质量对比**: 比较三个视频的生成质量\n")
            f.write("2. **LoRA效果**: 修复方法应该比基础模型有明显改进\n")
            f.write("3. **时间对比**: 检查推理时间是否合理\n")
            f.write("4. **一致性**: 验证LoRA是否按预期工作\n\n")
            
            f.write("## 预期结果\n")
            f.write("- ✅ 修复方法生成的视频质量应该最好\n")
            f.write("- ✅ LoRA应用后的效果应该比基础模型更符合训练数据特征\n")
            f.write("- ✅ 推理时间应该相近（LoRA不应显著增加推理时间）\n")

def main():
    """主函数"""
    tester = LoRAComparisonTester()
    tester.run_comparison_test()

if __name__ == "__main__":
    main()
